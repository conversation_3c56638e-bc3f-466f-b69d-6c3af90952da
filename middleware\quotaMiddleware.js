const Subscription = require('../models/Subscription');
const Permission = require('../models/Permission');

class QuotaMiddleware {
  // Check if user has a valid subscription plan
  static checkValidSubscription() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        // Check if user is admin (unlimited)
        const isAdmin = await Permission.isAdmin(req.session.userId);
        if (isAdmin) {
          return next();
        }

        // Check if user has an active subscription
        const subscription = await Subscription.getUserSubscription(req.session.userId);

        if (!subscription) {
          return res.status(403).json({
            error: 'No active subscription',
            message: 'You need an active subscription plan to create streams. Please subscribe to a plan first.',
            requiresSubscription: true
          });
        }

        // Check if subscription has expired
        if (subscription.end_date && new Date(subscription.end_date) < new Date()) {
          return res.status(403).json({
            error: 'Subscription expired',
            message: 'Your subscription has expired. Please renew to continue creating streams.',
            requiresSubscription: true
          });
        }

        next();
      } catch (error) {
        console.error('Subscription validation error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Check streaming slot quota
  static checkStreamingQuota() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        // Check if user is admin (unlimited)
        const isAdmin = await Permission.isAdmin(req.session.userId);
        if (isAdmin) {
          return next();
        }

        const quotaCheck = await Subscription.checkStreamingSlotLimit(req.session.userId);

        if (quotaCheck.hasLimit) {
          // Special message for Preview plan users (0 slots)
          const message = quotaCheck.maxSlots === 0
            ? 'Preview plan does not allow streaming. Please upgrade to Basic plan to start streaming.'
            : `You have reached your streaming limit of ${quotaCheck.maxSlots} concurrent streams. Please upgrade your plan or stop an existing stream.`;

          return res.status(403).json({
            error: quotaCheck.maxSlots === 0 ? 'Streaming not allowed' : 'Streaming slot limit reached',
            details: {
              currentSlots: quotaCheck.currentSlots,
              maxSlots: quotaCheck.maxSlots,
              message: message,
              isPreviewPlan: quotaCheck.maxSlots === 0
            }
          });
        }

        next();
      } catch (error) {
        console.error('Streaming quota check error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Check storage quota
  static checkStorageQuota(additionalSizeGB = 0) {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        // Check if user is admin (unlimited)
        const isAdmin = await Permission.isAdmin(req.session.userId);
        if (isAdmin) {
          return next();
        }

        // Calculate file size if it's an upload
        let fileSizeGB = additionalSizeGB;
        if (req.file && req.file.size) {
          fileSizeGB = req.file.size / (1024 * 1024 * 1024); // Convert bytes to GB
        } else if (req.files && req.files.length > 0) {
          fileSizeGB = req.files.reduce((total, file) => total + file.size, 0) / (1024 * 1024 * 1024);
        }

        const quotaCheck = await Subscription.checkStorageLimit(req.session.userId, fileSizeGB);

        if (quotaCheck.hasLimit) {
          return res.status(413).json({
            error: 'Storage limit exceeded',
            details: {
              currentStorage: quotaCheck.currentStorage,
              maxStorage: quotaCheck.maxStorage,
              availableStorage: quotaCheck.availableStorage,
              requestedSize: fileSizeGB,
              message: `This upload would exceed your storage limit. You have ${quotaCheck.availableStorage.toFixed(2)}GB available out of ${quotaCheck.maxStorage}GB total.`
            }
          });
        }

        // Store the file size for later use
        req.uploadSizeGB = fileSizeGB;
        next();
      } catch (error) {
        console.error('Storage quota check error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Update storage usage after successful upload
  static updateStorageUsage() {
    return async (req, res, next) => {
      try {
        if (req.uploadSizeGB && req.session.userId) {
          console.log(`📊 Updating storage: +${req.uploadSizeGB.toFixed(3)}GB for user ${req.session.userId}`);
          await Subscription.updateStorageUsage(req.session.userId, req.uploadSizeGB);
          console.log('✅ Storage updated successfully');
        }
        next();
      } catch (error) {
        console.error('❌ Storage usage update error:', error);
        // Don't fail the request, just log the error
        next();
      }
    };
  }

  // Check if user account is active
  static checkActiveAccount() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const User = require('../models/User');
        const user = await User.findById(req.session.userId);

        if (!user || !user.is_active) {
          req.session.destroy();
          return res.status(403).json({
            error: 'Account suspended',
            message: 'Your account has been suspended. Please contact support.'
          });
        }

        next();
      } catch (error) {
        console.error('Account status check error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Check subscription validity
  static checkSubscription() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        // Check if user is admin (bypass subscription check)
        const isAdmin = await Permission.isAdmin(req.session.userId);
        if (isAdmin) {
          return next();
        }

        const subscription = await Subscription.getUserSubscription(req.session.userId);

        if (!subscription) {
          return res.status(403).json({
            error: 'No active subscription',
            message: 'You need an active subscription to use this feature.'
          });
        }

        // Check if subscription has expired
        if (subscription.end_date && new Date(subscription.end_date) < new Date()) {
          return res.status(403).json({
            error: 'Subscription expired',
            message: 'Your subscription has expired. Please renew to continue using this feature.'
          });
        }

        next();
      } catch (error) {
        console.error('Subscription check error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Get user quota information
  static async getUserQuotaInfo(userId) {
    try {
      // Get user info first to check their plan
      const User = require('../models/User');
      const user = await User.findById(userId);

      if (!user) {
        return null;
      }

      const [streamingQuota, storageQuota, subscription] = await Promise.all([
        Subscription.checkStreamingSlotLimit(userId),
        Subscription.checkStorageLimit(userId),
        Subscription.getUserSubscription(userId)
      ]);

      // For users without subscription but with Preview/Basic plan, use plan limits
      const maxSlots = streamingQuota.maxSlots !== undefined ? streamingQuota.maxSlots : user.max_streaming_slots || 0;
      // Default to 1GB for Preview plan users
      const defaultStorage = user.plan_type === 'Preview' ? 1 : 2;
      const maxStorage = storageQuota.maxStorage !== undefined ? storageQuota.maxStorage : user.max_storage_gb || defaultStorage;
      const currentStorage = storageQuota.currentStorage !== undefined ? storageQuota.currentStorage : user.used_storage_gb || 0;

      // Format storage display - show in MB if 1GB or less, otherwise GB
      const formatStorageDisplay = (storageGB, maxStorageGB) => {
        if (maxStorageGB <= 1 && storageGB <= 1) {
          return {
            current: Math.round(storageGB * 1024),
            max: Math.round(maxStorageGB * 1024),
            unit: 'MB'
          };
        } else {
          return {
            current: parseFloat(storageGB.toFixed(2)),
            max: maxStorageGB,
            unit: 'GB'
          };
        }
      };

      const storageDisplay = formatStorageDisplay(currentStorage, maxStorage);

      return {
        streaming: {
          current: streamingQuota.currentSlots || 0,
          max: maxSlots === -1 ? -1 : maxSlots,
          available: maxSlots === -1 ? 'Unlimited' : Math.max(0, maxSlots - (streamingQuota.currentSlots || 0))
        },
        storage: {
          current: storageDisplay.current,
          currentGB: parseFloat(currentStorage.toFixed(2)), // Keep GB value for calculations
          max: storageDisplay.max,
          maxGB: maxStorage, // Keep GB value for calculations
          unit: storageDisplay.unit,
          available: Math.max(0, maxStorage - currentStorage),
          percentage: maxStorage > 0 ? ((currentStorage / maxStorage) * 100).toFixed(1) : 0
        },
        subscription: subscription ? {
          plan: subscription.plan_name,
          status: subscription.status,
          endDate: subscription.end_date
        } : {
          plan: user.plan_type || 'Preview',
          status: 'active',
          endDate: null
        }
      };
    } catch (error) {
      console.error('Error getting quota info:', error);
      return {
        streaming: { current: 0, max: 0, available: 0 },
        storage: { current: 0, max: 1, available: 1, percentage: 0 },
        subscription: { plan: 'Preview', status: 'active', endDate: null }
      };
    }
  }
}

module.exports = QuotaMiddleware;
