2025-05-30T11:03:37.641Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:03:45.087Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:03:45.090Z [LOG] Stream scheduler initialized
2025-05-30T11:03:45.091Z [LOG] Checking for scheduled streams (2025-05-30T11:03:45.091Z to 2025-05-30T11:04:45.091Z)
2025-05-30T11:03:45.173Z [LOG] StreamFlow running at:
2025-05-30T11:03:45.175Z [LOG]   http://**************:7575
2025-05-30T11:03:45.176Z [LOG]   http://************:7575
2025-05-30T11:03:45.177Z [LOG]   http://*************:7575
2025-05-30T11:03:45.178Z [LOG]   http://***********:7575
2025-05-30T11:03:45.190Z [ERROR] Error finding streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.196Z [ERROR] Error checking stream durations: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,  \n' +
  '               v.bitrate AS video_bitrate,        \n' +
  '               v.fps AS video_fps                 \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  '       ORDER BY s.created_at DESC', [], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:93:10
    at new Promise (<anonymous>)
    at Stream.findAll (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:65:12)
    at checkStreamDurations (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:48:38)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:11:3)
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:475:18)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.202Z [ERROR] Error finding streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.208Z [ERROR] Error resetting stream statuses: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,  \n' +
  '               v.bitrate AS video_bitrate,        \n' +
  '               v.fps AS video_fps                 \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  '       ORDER BY s.created_at DESC', [], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:93:10
    at new Promise (<anonymous>)
    at Stream.findAll (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:65:12)
    at Server.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:1478:34)
    at Object.onceWrapper (node:events:628:28)
    at Server.emit (node:events:526:35)
    at emitListeningNT (node:net:1906:10)
    at process.processTicksAndRejections (node:internal/process/task_queues:81:21) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.209Z [LOG] Stream scheduler initialized
2025-05-30T11:03:45.210Z [LOG] Checking for scheduled streams (2025-05-30T11:03:45.210Z to 2025-05-30T11:04:45.210Z)
2025-05-30T11:03:45.214Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:03:45.217Z [ERROR] Error finding scheduled streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.221Z [ERROR] Error checking scheduled streams: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,\n' +
  '               v.bitrate AS video_bitrate,\n' +
  '               v.fps AS video_fps  \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  "        WHERE s.status = 'scheduled'\n" +
  '        AND s.schedule_time IS NOT NULL\n' +
  '        AND s.schedule_time >= ?\n' +
  '        AND s.schedule_time <= ?\n' +
  '      ', [ '2025-05-30T11:03:45.091Z', '2025-05-30T11:04:45.091Z' ], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:244:10
    at new Promise (<anonymous>)
    at Stream.findScheduledInRange (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:225:12)
    at checkScheduledStreams (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:22:34)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:10:3)
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:475:18)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.225Z [ERROR] Error finding scheduled streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.231Z [ERROR] Error checking scheduled streams: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,\n' +
  '               v.bitrate AS video_bitrate,\n' +
  '               v.fps AS video_fps  \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  "        WHERE s.status = 'scheduled'\n" +
  '        AND s.schedule_time IS NOT NULL\n' +
  '        AND s.schedule_time >= ?\n' +
  '        AND s.schedule_time <= ?\n' +
  '      ', [ '2025-05-30T11:03:45.210Z', '2025-05-30T11:04:45.210Z' ], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:244:10
    at new Promise (<anonymous>)
    at Stream.findScheduledInRange (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:225:12)
    at checkScheduledStreams (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:22:34)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:10:3)
    at Server.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:1488:20)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.233Z [ERROR] Error finding streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.236Z [ERROR] Error checking stream durations: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,  \n' +
  '               v.bitrate AS video_bitrate,        \n' +
  '               v.fps AS video_fps                 \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  '       ORDER BY s.created_at DESC', [], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:93:10
    at new Promise (<anonymous>)
    at Stream.findAll (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:65:12)
    at checkStreamDurations (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:48:38)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:11:3)
    at Server.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:1488:20)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:03:45.239Z [ERROR] Error finding streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:03:45.242Z [ERROR] [StreamingService] Error syncing stream statuses: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,  \n' +
  '               v.bitrate AS video_bitrate,        \n' +
  '               v.fps AS video_fps                 \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  '       ORDER BY s.created_at DESC', [], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:93:10
    at new Promise (<anonymous>)
    at Stream.findAll (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:65:12)
    at Object.syncStreamStatuses (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:361:38)
    at Server.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:1490:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:05:35.760Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:05:36.483Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:05:36.486Z [LOG] Stream scheduler initialized
2025-05-30T11:05:36.487Z [LOG] Checking for scheduled streams (2025-05-30T11:05:36.487Z to 2025-05-30T11:06:36.487Z)
2025-05-30T11:05:36.545Z [LOG] StreamFlow running at:
2025-05-30T11:05:36.546Z [LOG]   http://**************:7575
2025-05-30T11:05:36.547Z [LOG]   http://************:7575
2025-05-30T11:05:36.548Z [LOG]   http://*************:7575
2025-05-30T11:05:36.549Z [LOG]   http://***********:7575
2025-05-30T11:05:36.553Z [LOG] Stream scheduler initialized
2025-05-30T11:05:36.555Z [LOG] Checking for scheduled streams (2025-05-30T11:05:36.555Z to 2025-05-30T11:06:36.555Z)
2025-05-30T11:05:36.557Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:05:36.558Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:06:20.106Z [LOG] Validation errors: [
  {
    type: 'field',
    value: 'aufanirsad',
    msg: 'Password must contain at least one uppercase letter',
    path: 'password',
    location: 'body'
  },
  {
    type: 'field',
    value: 'aufanirsad',
    msg: 'Password must contain at least one number',
    path: 'password',
    location: 'body'
  }
]
2025-05-30T11:06:36.501Z [LOG] Checking for scheduled streams (2025-05-30T11:06:36.500Z to 2025-05-30T11:07:36.500Z)
2025-05-30T11:06:36.571Z [LOG] Checking for scheduled streams (2025-05-30T11:06:36.571Z to 2025-05-30T11:07:36.571Z)
2025-05-30T11:06:39.665Z [LOG] Validation errors: [
  {
    type: 'field',
    value: 'Aufanirsad',
    msg: 'Password must contain at least one number',
    path: 'password',
    location: 'body'
  }
]
2025-05-30T11:06:59.176Z [LOG] User created successfully with ID: 32e5937c-7ef8-4586-bbc7-4373ea66bb69
2025-05-30T11:07:36.514Z [LOG] Checking for scheduled streams (2025-05-30T11:07:36.513Z to 2025-05-30T11:08:36.513Z)
2025-05-30T11:07:36.583Z [LOG] Checking for scheduled streams (2025-05-30T11:07:36.582Z to 2025-05-30T11:08:36.582Z)
2025-05-30T11:07:47.354Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748603267119-248628093.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748603267119-248628093.mp4',
  size: 42760993
}
2025-05-30T11:08:36.517Z [LOG] Checking for scheduled streams (2025-05-30T11:08:36.516Z to 2025-05-30T11:09:36.516Z)
2025-05-30T11:08:36.590Z [LOG] Checking for scheduled streams (2025-05-30T11:08:36.589Z to 2025-05-30T11:09:36.589Z)
2025-05-30T11:09:36.531Z [LOG] Checking for scheduled streams (2025-05-30T11:09:36.531Z to 2025-05-30T11:10:36.531Z)
2025-05-30T11:09:36.590Z [LOG] Checking for scheduled streams (2025-05-30T11:09:36.590Z to 2025-05-30T11:10:36.590Z)
2025-05-30T11:10:36.506Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:10:36.509Z [LOG] [StreamingService] Found inconsistent stream 89ba35ad-7dfa-406c-989a-fd3901927a36: marked as 'live' in DB but not active in memory
2025-05-30T11:10:36.511Z [LOG] [StreamingService] Updated stream 89ba35ad-7dfa-406c-989a-fd3901927a36 status to 'offline'
2025-05-30T11:10:36.513Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:10:36.537Z [LOG] Checking for scheduled streams (2025-05-30T11:10:36.537Z to 2025-05-30T11:11:36.537Z)
2025-05-30T11:10:36.599Z [LOG] Checking for scheduled streams (2025-05-30T11:10:36.599Z to 2025-05-30T11:11:36.599Z)
2025-05-30T11:11:02.434Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:11:02.436Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 720x1280 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:11:36.546Z [LOG] Checking for scheduled streams (2025-05-30T11:11:36.546Z to 2025-05-30T11:12:36.546Z)
2025-05-30T11:11:36.608Z [LOG] Checking for scheduled streams (2025-05-30T11:11:36.608Z to 2025-05-30T11:12:36.608Z)
2025-05-30T11:12:36.548Z [LOG] Checking for scheduled streams (2025-05-30T11:12:36.547Z to 2025-05-30T11:13:36.547Z)
2025-05-30T11:12:36.616Z [LOG] Checking for scheduled streams (2025-05-30T11:12:36.615Z to 2025-05-30T11:13:36.615Z)
2025-05-30T11:13:36.556Z [LOG] Checking for scheduled streams (2025-05-30T11:13:36.556Z to 2025-05-30T11:14:36.556Z)
2025-05-30T11:13:36.623Z [LOG] Checking for scheduled streams (2025-05-30T11:13:36.623Z to 2025-05-30T11:14:36.623Z)
2025-05-30T11:14:36.568Z [LOG] Checking for scheduled streams (2025-05-30T11:14:36.567Z to 2025-05-30T11:15:36.567Z)
2025-05-30T11:14:36.634Z [LOG] Checking for scheduled streams (2025-05-30T11:14:36.634Z to 2025-05-30T11:15:36.634Z)
2025-05-30T11:15:24.027Z [LOG] [StreamingService] Stop request for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, isActive: true
2025-05-30T11:15:24.028Z [LOG] [StreamingService] Stopping active stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:15:24.053Z [LOG] [StreamingService] Stream history saved for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, duration: 261s
2025-05-30T11:15:24.055Z [LOG] Reset schedule_time for stopped stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:15:24.058Z [LOG] [FFMPEG_EXIT] 89ba35ad-7dfa-406c-989a-fd3901927a36: Code=null, Signal=SIGTERM
2025-05-30T11:15:24.061Z [LOG] [StreamingService] Stream 89ba35ad-7dfa-406c-989a-fd3901927a36 was manually stopped, not restarting
2025-05-30T11:15:36.510Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:15:36.514Z [LOG] [StreamingService] Found inconsistent stream 89ba35ad-7dfa-406c-989a-fd3901927a36: marked as 'live' in DB but not active in memory
2025-05-30T11:15:36.517Z [LOG] [StreamingService] Updated stream 89ba35ad-7dfa-406c-989a-fd3901927a36 status to 'offline'
2025-05-30T11:15:36.518Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:15:36.570Z [LOG] Checking for scheduled streams (2025-05-30T11:15:36.570Z to 2025-05-30T11:16:36.570Z)
2025-05-30T11:15:36.579Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:15:36.581Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:15:36.634Z [LOG] Checking for scheduled streams (2025-05-30T11:15:36.634Z to 2025-05-30T11:16:36.634Z)
2025-05-30T11:16:36.572Z [LOG] Checking for scheduled streams (2025-05-30T11:16:36.571Z to 2025-05-30T11:17:36.571Z)
2025-05-30T11:16:36.648Z [LOG] Checking for scheduled streams (2025-05-30T11:16:36.648Z to 2025-05-30T11:17:36.648Z)
2025-05-30T11:16:42.766Z [LOG] [StreamingService] Stop request for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, isActive: true
2025-05-30T11:16:42.768Z [LOG] [StreamingService] Stopping active stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:16:42.784Z [LOG] [StreamingService] Stream history saved for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, duration: 66s
2025-05-30T11:16:42.785Z [LOG] Reset schedule_time for stopped stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:16:42.789Z [LOG] [FFMPEG_EXIT] 89ba35ad-7dfa-406c-989a-fd3901927a36: Code=null, Signal=SIGTERM
2025-05-30T11:16:42.790Z [LOG] [StreamingService] Stream 89ba35ad-7dfa-406c-989a-fd3901927a36 was manually stopped, not restarting
2025-05-30T11:16:58.008Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:16:58.010Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:17:36.579Z [LOG] Checking for scheduled streams (2025-05-30T11:17:36.578Z to 2025-05-30T11:18:36.578Z)
2025-05-30T11:17:36.656Z [LOG] Checking for scheduled streams (2025-05-30T11:17:36.655Z to 2025-05-30T11:18:36.655Z)
2025-05-30T11:18:04.922Z [LOG] [StreamingService] Stop request for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, isActive: true
2025-05-30T11:18:04.923Z [LOG] [StreamingService] Stopping active stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:18:04.942Z [LOG] [StreamingService] Stream history saved for stream 89ba35ad-7dfa-406c-989a-fd3901927a36, duration: 66s
2025-05-30T11:18:04.944Z [LOG] Reset schedule_time for stopped stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:18:04.947Z [LOG] [FFMPEG_EXIT] 89ba35ad-7dfa-406c-989a-fd3901927a36: Code=null, Signal=SIGTERM
2025-05-30T11:18:04.948Z [LOG] [StreamingService] Stream 89ba35ad-7dfa-406c-989a-fd3901927a36 was manually stopped, not restarting
2025-05-30T11:18:25.851Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:18:25.853Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:18:36.583Z [LOG] Checking for scheduled streams (2025-05-30T11:18:36.583Z to 2025-05-30T11:19:36.583Z)
2025-05-30T11:18:36.660Z [LOG] Checking for scheduled streams (2025-05-30T11:18:36.660Z to 2025-05-30T11:19:36.660Z)
2025-05-30T11:19:35.688Z [ERROR] ValidationError: An undefined 'request.ip' was detected. This might indicate a misconfiguration or the connection being destroyed prematurely. See https://express-rate-limit.github.io/ERR_ERL_UNDEFINED_IP_ADDRESS/ for more information.
    at Object.ip (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:146:13)
    at wrappedValidations.<computed> [as ip] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:398:22)
    at Object.keyGenerator (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:669:20)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:724:32
    at async C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express-rate-limit\dist\index.cjs:704:5 {
  code: 'ERR_ERL_UNDEFINED_IP_ADDRESS',
  help: 'https://express-rate-limit.github.io/ERR_ERL_UNDEFINED_IP_ADDRESS/'
}
2025-05-30T11:19:36.583Z [LOG] Checking for scheduled streams (2025-05-30T11:19:36.582Z to 2025-05-30T11:20:36.582Z)
2025-05-30T11:19:36.661Z [LOG] Checking for scheduled streams (2025-05-30T11:19:36.660Z to 2025-05-30T11:20:36.660Z)
2025-05-30T11:20:36.522Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:20:36.524Z [LOG] [StreamingService] Stream status sync completed. Active streams: 1
2025-05-30T11:20:36.585Z [LOG] Checking for scheduled streams (2025-05-30T11:20:36.584Z to 2025-05-30T11:21:36.584Z)
2025-05-30T11:20:36.676Z [LOG] Checking for scheduled streams (2025-05-30T11:20:36.675Z to 2025-05-30T11:21:36.675Z)
2025-05-30T11:21:36.591Z [LOG] Checking for scheduled streams (2025-05-30T11:21:36.590Z to 2025-05-30T11:22:36.590Z)
2025-05-30T11:21:36.690Z [LOG] Checking for scheduled streams (2025-05-30T11:21:36.689Z to 2025-05-30T11:22:36.689Z)
2025-05-30T11:22:36.598Z [LOG] Checking for scheduled streams (2025-05-30T11:22:36.597Z to 2025-05-30T11:23:36.597Z)
2025-05-30T11:22:36.698Z [LOG] Checking for scheduled streams (2025-05-30T11:22:36.697Z to 2025-05-30T11:23:36.697Z)
2025-05-30T11:23:36.606Z [LOG] Checking for scheduled streams (2025-05-30T11:23:36.605Z to 2025-05-30T11:24:36.605Z)
2025-05-30T11:23:36.699Z [LOG] Checking for scheduled streams (2025-05-30T11:23:36.699Z to 2025-05-30T11:24:36.699Z)
2025-05-30T11:24:36.610Z [LOG] Checking for scheduled streams (2025-05-30T11:24:36.610Z to 2025-05-30T11:25:36.610Z)
2025-05-30T11:24:36.700Z [LOG] Checking for scheduled streams (2025-05-30T11:24:36.700Z to 2025-05-30T11:25:36.700Z)
2025-05-30T11:35:05.430Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:35:06.293Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:35:06.295Z [LOG] Stream scheduler initialized
2025-05-30T11:35:06.296Z [LOG] Checking for scheduled streams (2025-05-30T11:35:06.296Z to 2025-05-30T11:36:06.296Z)
2025-05-30T11:35:06.348Z [LOG] StreamFlow running at:
2025-05-30T11:35:06.349Z [LOG]   http://**************:7575
2025-05-30T11:35:06.350Z [LOG]   http://************:7575
2025-05-30T11:35:06.351Z [LOG]   http://*************:7575
2025-05-30T11:35:06.352Z [LOG]   http://***********:7575
2025-05-30T11:35:06.353Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T11:35:06.358Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T11:35:06.359Z [LOG] [StreamingService] Stream 89ba35ad-7dfa-406c-989a-fd3901927a36 has been running for 16 minutes, attempting to restore tracking...
2025-05-30T11:35:06.363Z [LOG] [StreamingService] Using re-encoding mode for stream 89ba35ad-7dfa-406c-989a-fd3901927a36 (video needs optimization)
2025-05-30T11:35:06.365Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748603267119-248628093.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:35:06.382Z [LOG] [StreamingService] Successfully restored tracking for stream 89ba35ad-7dfa-406c-989a-fd3901927a36
2025-05-30T11:35:06.383Z [LOG] Stream scheduler initialized
2025-05-30T11:35:06.384Z [LOG] Checking for scheduled streams (2025-05-30T11:35:06.384Z to 2025-05-30T11:36:06.384Z)
2025-05-30T11:35:06.386Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:35:06.390Z [LOG] [StreamingService] Stream status sync completed. Active streams: 1
2025-05-30T11:36:06.305Z [LOG] Checking for scheduled streams (2025-05-30T11:36:06.303Z to 2025-05-30T11:37:06.303Z)
2025-05-30T11:36:06.401Z [LOG] Checking for scheduled streams (2025-05-30T11:36:06.400Z to 2025-05-30T11:37:06.400Z)
2025-05-30T11:37:06.316Z [LOG] Checking for scheduled streams (2025-05-30T11:37:06.315Z to 2025-05-30T11:38:06.315Z)
2025-05-30T11:37:06.401Z [LOG] Checking for scheduled streams (2025-05-30T11:37:06.401Z to 2025-05-30T11:38:06.401Z)
2025-05-30T11:37:40.685Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:37:41.511Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:37:41.513Z [LOG] Stream scheduler initialized
2025-05-30T11:37:41.515Z [LOG] Checking for scheduled streams (2025-05-30T11:37:41.514Z to 2025-05-30T11:38:41.514Z)
2025-05-30T11:37:41.592Z [LOG] StreamFlow running at:
2025-05-30T11:37:41.593Z [LOG]   http://**************:7575
2025-05-30T11:37:41.594Z [LOG]   http://************:7575
2025-05-30T11:37:41.595Z [LOG]   http://*************:7575
2025-05-30T11:37:41.597Z [LOG]   http://***********:7575
2025-05-30T11:37:41.599Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T11:37:41.605Z [ERROR] Error finding scheduled streams: SQLITE_ERROR: no such table: streams
2025-05-30T11:37:41.613Z [ERROR] Error checking scheduled streams: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,\n' +
  '               v.bitrate AS video_bitrate,\n' +
  '               v.fps AS video_fps  \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  "        WHERE s.status = 'scheduled'\n" +
  '        AND s.schedule_time IS NOT NULL\n' +
  '        AND s.schedule_time >= ?\n' +
  '        AND s.schedule_time <= ?\n' +
  '      ', [ '2025-05-30T11:37:41.514Z', '2025-05-30T11:38:41.514Z' ], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:244:10
    at new Promise (<anonymous>)
    at Stream.findScheduledInRange (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:225:12)
    at checkScheduledStreams (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:22:34)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:10:3)
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:533:18)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T11:37:41.644Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T11:37:41.645Z [LOG] Stream scheduler initialized
2025-05-30T11:37:41.646Z [LOG] Checking for scheduled streams (2025-05-30T11:37:41.646Z to 2025-05-30T11:38:41.646Z)
2025-05-30T11:37:41.649Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:37:41.666Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:37:49.302Z [LOG] User created successfully with ID: 1748e095-78ae-4b25-820f-4ee640e3ba74
2025-05-30T11:38:30.993Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748605110699-834158579.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748605110699-834158579.mp4',
  size: 42760993
}
2025-05-30T11:38:41.513Z [LOG] Checking for scheduled streams (2025-05-30T11:38:41.512Z to 2025-05-30T11:39:41.512Z)
2025-05-30T11:38:41.655Z [LOG] Checking for scheduled streams (2025-05-30T11:38:41.654Z to 2025-05-30T11:39:41.654Z)
2025-05-30T11:39:39.203Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:39:39.941Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:39:39.946Z [LOG] Stream scheduler initialized
2025-05-30T11:39:39.948Z [LOG] Checking for scheduled streams (2025-05-30T11:39:39.948Z to 2025-05-30T11:40:39.948Z)
2025-05-30T11:39:40.010Z [LOG] StreamFlow running at:
2025-05-30T11:39:40.011Z [LOG]   http://**************:7575
2025-05-30T11:39:40.012Z [LOG]   http://************:7575
2025-05-30T11:39:40.013Z [LOG]   http://*************:7575
2025-05-30T11:39:40.014Z [LOG]   http://***********:7575
2025-05-30T11:39:40.015Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T11:39:40.019Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T11:39:40.020Z [LOG] Stream scheduler initialized
2025-05-30T11:39:40.021Z [LOG] Checking for scheduled streams (2025-05-30T11:39:40.020Z to 2025-05-30T11:40:40.020Z)
2025-05-30T11:39:40.022Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:39:40.024Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:40:00.483Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748605200256-688601605.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748605200256-688601605.mp4',
  size: 42760993
}
2025-05-30T11:40:39.958Z [LOG] Checking for scheduled streams (2025-05-30T11:40:39.958Z to 2025-05-30T11:41:39.958Z)
2025-05-30T11:40:40.027Z [LOG] Checking for scheduled streams (2025-05-30T11:40:40.027Z to 2025-05-30T11:41:40.027Z)
2025-05-30T11:41:39.971Z [LOG] Checking for scheduled streams (2025-05-30T11:41:39.969Z to 2025-05-30T11:42:39.969Z)
2025-05-30T11:41:40.039Z [LOG] Checking for scheduled streams (2025-05-30T11:41:40.038Z to 2025-05-30T11:42:40.038Z)
2025-05-30T11:42:39.988Z [LOG] Checking for scheduled streams (2025-05-30T11:42:39.987Z to 2025-05-30T11:43:39.987Z)
2025-05-30T11:42:40.053Z [LOG] Checking for scheduled streams (2025-05-30T11:42:40.053Z to 2025-05-30T11:43:40.053Z)
2025-05-30T11:43:39.989Z [LOG] Checking for scheduled streams (2025-05-30T11:43:39.988Z to 2025-05-30T11:44:39.988Z)
2025-05-30T11:43:40.059Z [LOG] Checking for scheduled streams (2025-05-30T11:43:40.059Z to 2025-05-30T11:44:40.059Z)
2025-05-30T11:44:39.952Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:44:39.955Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T11:44:39.956Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T11:44:39.957Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:44:40.003Z [LOG] Checking for scheduled streams (2025-05-30T11:44:40.003Z to 2025-05-30T11:45:40.003Z)
2025-05-30T11:44:40.071Z [LOG] Checking for scheduled streams (2025-05-30T11:44:40.070Z to 2025-05-30T11:45:40.070Z)
2025-05-30T11:45:40.012Z [LOG] Checking for scheduled streams (2025-05-30T11:45:40.011Z to 2025-05-30T11:46:40.011Z)
2025-05-30T11:45:40.083Z [LOG] Checking for scheduled streams (2025-05-30T11:45:40.078Z to 2025-05-30T11:46:40.078Z)
2025-05-30T11:46:40.021Z [LOG] Checking for scheduled streams (2025-05-30T11:46:40.021Z to 2025-05-30T11:47:40.021Z)
2025-05-30T11:46:40.088Z [LOG] Checking for scheduled streams (2025-05-30T11:46:40.088Z to 2025-05-30T11:47:40.088Z)
2025-05-30T11:47:40.034Z [LOG] Checking for scheduled streams (2025-05-30T11:47:40.034Z to 2025-05-30T11:48:40.034Z)
2025-05-30T11:47:40.100Z [LOG] Checking for scheduled streams (2025-05-30T11:47:40.099Z to 2025-05-30T11:48:40.099Z)
2025-05-30T11:48:35.473Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:48:36.304Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:48:36.306Z [LOG] Stream scheduler initialized
2025-05-30T11:48:36.307Z [LOG] Checking for scheduled streams (2025-05-30T11:48:36.306Z to 2025-05-30T11:49:36.306Z)
2025-05-30T11:48:36.353Z [ERROR] -----------------------------------
2025-05-30T11:48:36.357Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-05-30T11:48:36.358Z [ERROR] -----------------------------------
2025-05-30T11:48:40.040Z [LOG] Checking for scheduled streams (2025-05-30T11:48:40.039Z to 2025-05-30T11:49:40.039Z)
2025-05-30T11:48:40.105Z [LOG] Checking for scheduled streams (2025-05-30T11:48:40.104Z to 2025-05-30T11:49:40.104Z)
2025-05-30T11:49:36.319Z [LOG] Checking for scheduled streams (2025-05-30T11:49:36.318Z to 2025-05-30T11:50:36.318Z)
2025-05-30T11:49:39.969Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:49:39.972Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T11:49:39.974Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T11:49:39.976Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:49:40.051Z [LOG] Checking for scheduled streams (2025-05-30T11:49:40.050Z to 2025-05-30T11:50:40.050Z)
2025-05-30T11:49:40.120Z [LOG] Checking for scheduled streams (2025-05-30T11:49:40.120Z to 2025-05-30T11:50:40.120Z)
2025-05-30T11:50:40.065Z [LOG] Checking for scheduled streams (2025-05-30T11:50:40.064Z to 2025-05-30T11:51:40.064Z)
2025-05-30T11:50:40.131Z [LOG] Checking for scheduled streams (2025-05-30T11:50:40.131Z to 2025-05-30T11:51:40.131Z)
2025-05-30T11:51:40.073Z [LOG] Checking for scheduled streams (2025-05-30T11:51:40.073Z to 2025-05-30T11:52:40.073Z)
2025-05-30T11:51:40.138Z [LOG] Checking for scheduled streams (2025-05-30T11:51:40.138Z to 2025-05-30T11:52:40.138Z)
2025-05-30T11:52:18.161Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:18.179Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:19.338Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000023916d70140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:19.343Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:19.344Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:19.345Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:22.354Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:22.367Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:23.453Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000226f6230140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:23.460Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:23.462Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:23.462Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:26.470Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:26.488Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:26.948Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000026a7aa20140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:26.956Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:26.957Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:26.958Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:29.970Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:29.986Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:30.502Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000025f346a0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:30.510Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:30.511Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:30.512Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:33.518Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:33.530Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:34.628Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000017591c60140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:34.635Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:34.637Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:34.637Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:37.651Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:37.664Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:38.134Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000201a9280140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:38.139Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:38.140Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:38.141Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:40.084Z [LOG] Checking for scheduled streams (2025-05-30T11:52:40.084Z to 2025-05-30T11:53:40.084Z)
2025-05-30T11:52:40.139Z [LOG] Checking for scheduled streams (2025-05-30T11:52:40.138Z to 2025-05-30T11:53:40.138Z)
2025-05-30T11:52:41.152Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:41.166Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:41.648Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000020ea6110140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:41.656Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:41.657Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:41.658Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:44.668Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:44.686Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:45.153Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000029a78090140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:45.159Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:45.160Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:45.162Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:48.168Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:48.188Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:49.670Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001fb7e900140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:49.676Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:49.679Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:49.680Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:52.698Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:52.709Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:53.791Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000287f9d40140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:53.796Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:53.798Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:53.799Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:56.814Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:52:56.827Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:52:57.303Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000029afe850140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:52:57.308Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:52:57.309Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:52:57.310Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:00.317Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:00.332Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:00.821Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001b5a8960140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:00.827Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:00.828Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:00.832Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:03.848Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:03.860Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:04.338Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000021bd6e70140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:04.344Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:04.345Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:04.348Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:07.352Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:07.367Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:07.822Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001d07bf90140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:07.830Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:07.831Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:07.833Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:10.848Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:10.860Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:12.357Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000233d28d0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:12.363Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:12.364Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:12.365Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:15.382Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:15.392Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:16.864Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000158db040140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:16.870Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:16.872Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:16.873Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:19.886Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:19.899Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:21.408Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001ff72230140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:21.416Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:21.417Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:21.418Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:24.430Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:24.441Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:24.894Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002a4dfc00140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:24.899Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:24.900Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:24.902Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:27.912Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:53:27.923Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:53:28.403Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000020c1cd50140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:53:28.409Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:53:28.411Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:53:28.412Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:01.637Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T11:54:02.393Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T11:54:02.394Z [LOG] Stream scheduler initialized
2025-05-30T11:54:02.396Z [LOG] Checking for scheduled streams (2025-05-30T11:54:02.395Z to 2025-05-30T11:55:02.395Z)
2025-05-30T11:54:02.466Z [LOG] StreamFlow running at:
2025-05-30T11:54:02.467Z [LOG]   http://**************:7575
2025-05-30T11:54:02.468Z [LOG]   http://************:7575
2025-05-30T11:54:02.469Z [LOG]   http://*************:7575
2025-05-30T11:54:02.470Z [LOG]   http://***********:7575
2025-05-30T11:54:02.471Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T11:54:02.503Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 1.4236 minutes
2025-05-30T11:54:02.505Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T11:54:02.506Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was recently started (34 seconds ago), marking as offline for safety
2025-05-30T11:54:02.515Z [LOG] Stream scheduler initialized
2025-05-30T11:54:02.516Z [LOG] Checking for scheduled streams (2025-05-30T11:54:02.515Z to 2025-05-30T11:55:02.515Z)
2025-05-30T11:54:02.519Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T11:54:02.522Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T11:54:02.525Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T11:54:02.526Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T11:54:18.036Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T11:54:18.037Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T11:54:18.045Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:18.048Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:29.652Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:54:29.666Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:54:30.818Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001e4c6b60140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:54:30.825Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:54:30.826Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:30.827Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:33.840Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:54:33.855Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:54:34.949Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001f8da460140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T11:54:34.956Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T11:54:34.957Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:34.958Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:37.968Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T11:54:37.982Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T11:54:38.413Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: true
2025-05-30T11:54:38.415Z [LOG] [StreamingService] Stopping active stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:38.422Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=null, Signal=SIGTERM
2025-05-30T11:54:38.423Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was manually stopped, not restarting
2025-05-30T11:54:38.426Z [LOG] [StreamingService] Not saving history for stream e4ea3671-551a-4a7d-af09-d049aa38c001 - duration too short (0s)
2025-05-30T11:54:38.428Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T11:54:38.430Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:51.060Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:00:51.721Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:00:51.724Z [LOG] Stream scheduler initialized
2025-05-30T12:00:51.725Z [LOG] Checking for scheduled streams (2025-05-30T12:00:51.724Z to 2025-05-30T12:01:51.724Z)
2025-05-30T12:00:51.786Z [LOG] StreamFlow running at:
2025-05-30T12:00:51.787Z [LOG]   http://**************:7575
2025-05-30T12:00:51.788Z [LOG]   http://************:7575
2025-05-30T12:00:51.789Z [LOG]   http://*************:7575
2025-05-30T12:00:51.790Z [LOG]   http://***********:7575
2025-05-30T12:00:51.791Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:00:51.825Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:00:51.827Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:00:51.829Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T12:00:51.830Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 has been running for 6 minutes, attempting to restore tracking...
2025-05-30T12:00:51.862Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:00:51.888Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:00:51.891Z [LOG] [StreamingService] Successfully restored tracking for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:51.893Z [LOG] Stream scheduler initialized
2025-05-30T12:00:51.895Z [LOG] Checking for scheduled streams (2025-05-30T12:00:51.894Z to 2025-05-30T12:01:51.894Z)
2025-05-30T12:00:51.897Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:00:51.907Z [LOG] [StreamingService] Stream status sync completed. Active streams: 1
2025-05-30T12:00:52.432Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000174c26a0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are:
2025-05-30T12:00:52.434Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:00:52.438Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:00:52.439Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:52.440Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:55.459Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:00:55.474Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:00:58.993Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002505f320140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:00:58.998Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:00:58.999Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:00:59.000Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:02.010Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:02.036Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:03.253Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000024c352b0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:03.260Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:03.261Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:03.262Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:06.274Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:06.289Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:06.740Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002afefdb0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:06.746Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:06.747Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:06.748Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:09.757Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:09.779Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:13.257Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000013c52d30140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:13.263Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:13.264Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:13.265Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:16.271Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:16.284Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:16.735Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000027727f60140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:16.742Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:16.743Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:16.744Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:19.760Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:19.771Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:20.230Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000023615e40140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:20.235Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:20.236Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:20.238Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:23.245Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:23.259Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:23.761Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000026322230140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:23.767Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:23.768Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:23.770Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:26.789Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:26.806Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:27.910Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002998d600140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:27.915Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:27.916Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:27.917Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:30.938Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:30.955Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:31.430Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001fd55040140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:31.436Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:31.438Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:31.439Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:33.437Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:01:33.438Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:01:33.445Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:33.447Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:34.451Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:34.466Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:35.596Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001e16dc80140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:35.601Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:35.603Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:35.604Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:38.620Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:38.633Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:42.804Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001f915f30140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:42.810Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:42.811Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:42.812Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:44.374Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:01:44.376Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:01:44.385Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:44.387Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:45.820Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:45.832Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:47.345Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000028ef58b0140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:47.352Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:47.353Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:47.355Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:50.370Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:50.381Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:51.739Z [LOG] Checking for scheduled streams (2025-05-30T12:01:51.738Z to 2025-05-30T12:02:51.738Z)
2025-05-30T12:01:51.853Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001d727430140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:51.860Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:51.862Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:51.863Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:51.908Z [LOG] Checking for scheduled streams (2025-05-30T12:01:51.908Z to 2025-05-30T12:02:51.908Z)
2025-05-30T12:01:52.247Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:01:52.249Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:01:52.256Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:52.258Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:54.869Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:54.885Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:55.349Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001b7dff50140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:55.354Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:55.355Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:55.357Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:58.362Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:01:58.376Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:01:58.843Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001f8eb360140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:01:58.849Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:01:58.850Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:01:58.852Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:02:13.269Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:02:13.915Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:02:13.917Z [LOG] Stream scheduler initialized
2025-05-30T12:02:13.918Z [LOG] Checking for scheduled streams (2025-05-30T12:02:13.918Z to 2025-05-30T12:03:13.918Z)
2025-05-30T12:02:13.977Z [LOG] StreamFlow running at:
2025-05-30T12:02:13.978Z [LOG]   http://**************:7575
2025-05-30T12:02:13.978Z [LOG]   http://************:7575
2025-05-30T12:02:13.979Z [LOG]   http://*************:7575
2025-05-30T12:02:13.980Z [LOG]   http://***********:7575
2025-05-30T12:02:13.981Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:02:13.986Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T12:02:13.987Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was recently started (15 seconds ago), marking as offline for safety
2025-05-30T12:02:13.989Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 1.7396833333333332 minutes
2025-05-30T12:02:14.002Z [LOG] Stream scheduler initialized
2025-05-30T12:02:14.003Z [LOG] Checking for scheduled streams (2025-05-30T12:02:14.003Z to 2025-05-30T12:03:14.003Z)
2025-05-30T12:02:14.005Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:02:14.019Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:02:14.025Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:02:14.027Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:02:31.504Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:02:31.506Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:02:31.513Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:02:31.516Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:02:57.962Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:02:57.980Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:02:58.468Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002247ab70140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:02:58.474Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:02:58.475Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:02:58.476Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:01.489Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:01.502Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:01.964Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002a851100140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:01.969Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:01.971Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:01.973Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:04.989Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:05.003Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:05.487Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000002a68bd60140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:05.493Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:05.494Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:05.495Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:07.982Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:03:07.984Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:03:07.993Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:07.999Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:08.504Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:08.517Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:09.659Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 00000278aa420140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:09.667Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:09.671Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:09.672Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:12.689Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:12.700Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:13.346Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 000001b284230140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:13.352Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:13.354Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:13.355Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:13.930Z [LOG] Checking for scheduled streams (2025-05-30T12:03:13.929Z to 2025-05-30T12:04:13.929Z)
2025-05-30T12:03:14.014Z [LOG] Checking for scheduled streams (2025-05-30T12:03:14.013Z to 2025-05-30T12:04:14.013Z)
2025-05-30T12:03:14.577Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:03:14.578Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:03:14.586Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:14.587Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:16.365Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:16.377Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:17.840Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000013fffd40140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:17.845Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:17.846Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:17.847Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:20.864Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:20.874Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:21.332Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000028e9e620140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:21.338Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:21.338Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:21.340Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:22.031Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:03:22.033Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T12:03:22.040Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:22.042Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:24.345Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:24.360Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:24.913Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000026440630140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:24.918Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:24.919Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:24.920Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:27.931Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:27.942Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:28.418Z [ERROR] [FFMPEG_STDERR] e4ea3671-551a-4a7d-af09-d049aa38c001: [AVBSFContext @ 0000013b71730140] Codec 'hevc' (173) is not supported by the bitstream filter 'h264_mp4toannexb'. Supported codecs are: h264 (27) 
Error initializing bitstream filter: h264_mp4toannexb
2025-05-30T12:03:28.423Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=1, Signal=null
2025-05-30T12:03:28.424Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:28.425Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:31.430Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748605200256-688601605.mp4 -c:v copy -c:a copy -bsf:v h264_mp4toannexb -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:03:31.440Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:03:32.149Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: true
2025-05-30T12:03:32.150Z [LOG] [StreamingService] Stopping active stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:32.157Z [LOG] [StreamingService] Not saving history for stream e4ea3671-551a-4a7d-af09-d049aa38c001 - duration too short (0s)
2025-05-30T12:03:32.158Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:32.159Z [LOG] Reset schedule_time for stopped stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:03:32.162Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=null, Signal=SIGTERM
2025-05-30T12:03:32.163Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was manually stopped, not restarting
2025-05-30T12:03:52.153Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748606631922-224154124.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748606631922-224154124.mp4',
  size: 42760993
}
2025-05-30T12:03:52.296Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T12:04:13.938Z [LOG] Checking for scheduled streams (2025-05-30T12:04:13.937Z to 2025-05-30T12:05:13.937Z)
2025-05-30T12:04:13.941Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 1.2915666666666668 minutes
2025-05-30T12:04:14.025Z [LOG] Checking for scheduled streams (2025-05-30T12:04:14.025Z to 2025-05-30T12:05:14.025Z)
2025-05-30T12:04:27.760Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:04:27.761Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748606631922-224154124.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:04:27.777Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:05:13.948Z [LOG] Checking for scheduled streams (2025-05-30T12:05:13.947Z to 2025-05-30T12:06:13.947Z)
2025-05-30T12:05:14.029Z [LOG] Checking for scheduled streams (2025-05-30T12:05:14.028Z to 2025-05-30T12:06:14.028Z)
2025-05-30T12:06:13.958Z [LOG] Checking for scheduled streams (2025-05-30T12:06:13.958Z to 2025-05-30T12:07:13.958Z)
2025-05-30T12:06:14.037Z [LOG] Checking for scheduled streams (2025-05-30T12:06:14.036Z to 2025-05-30T12:07:14.036Z)
2025-05-30T12:06:27.792Z [LOG] Terminating stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minute duration
2025-05-30T12:06:27.796Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: true
2025-05-30T12:06:27.797Z [LOG] [StreamingService] Stopping active stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:06:27.817Z [LOG] [StreamingService] Stream history saved for stream e4ea3671-551a-4a7d-af09-d049aa38c001, duration: 2m 0s
2025-05-30T12:06:27.819Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:06:27.825Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=null, Signal=SIGTERM
2025-05-30T12:06:27.827Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was manually stopped, not restarting
2025-05-30T12:07:13.941Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:07:13.943Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:07:13.944Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:07:13.945Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:07:13.969Z [LOG] Checking for scheduled streams (2025-05-30T12:07:13.969Z to 2025-05-30T12:08:13.969Z)
2025-05-30T12:07:13.972Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:07:13.973Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:07:14.044Z [LOG] Checking for scheduled streams (2025-05-30T12:07:14.043Z to 2025-05-30T12:08:14.043Z)
2025-05-30T12:07:14.046Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:07:14.047Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:07:37.239Z [LOG] [StreamingService] Video uses HEVC codec, re-encoding required for streaming compatibility
2025-05-30T12:07:37.240Z [LOG] [StreamingService] Using re-encoding mode for stream e4ea3671-551a-4a7d-af09-d049aa38c001 (video needs optimization)
2025-05-30T12:07:37.241Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748606631922-224154124.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:07:37.254Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:08:13.972Z [LOG] Checking for scheduled streams (2025-05-30T12:08:13.971Z to 2025-05-30T12:09:13.971Z)
2025-05-30T12:08:14.048Z [LOG] Checking for scheduled streams (2025-05-30T12:08:14.047Z to 2025-05-30T12:09:14.047Z)
2025-05-30T12:09:13.972Z [LOG] Checking for scheduled streams (2025-05-30T12:09:13.972Z to 2025-05-30T12:10:13.972Z)
2025-05-30T12:09:14.050Z [LOG] Checking for scheduled streams (2025-05-30T12:09:14.050Z to 2025-05-30T12:10:14.050Z)
2025-05-30T12:09:37.269Z [LOG] Terminating stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minute duration
2025-05-30T12:09:37.271Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: true
2025-05-30T12:09:37.272Z [LOG] [StreamingService] Stopping active stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:09:37.284Z [LOG] [StreamingService] Stream history saved for stream e4ea3671-551a-4a7d-af09-d049aa38c001, duration: 2m 0s
2025-05-30T12:09:37.285Z [LOG] Cancelled scheduled termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:09:37.291Z [LOG] [FFMPEG_EXIT] e4ea3671-551a-4a7d-af09-d049aa38c001: Code=null, Signal=SIGTERM
2025-05-30T12:09:37.292Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 was manually stopped, not restarting
2025-05-30T12:10:13.976Z [LOG] Checking for scheduled streams (2025-05-30T12:10:13.976Z to 2025-05-30T12:11:13.976Z)
2025-05-30T12:10:13.979Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:10:13.980Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:10:14.055Z [LOG] Checking for scheduled streams (2025-05-30T12:10:14.054Z to 2025-05-30T12:11:14.054Z)
2025-05-30T12:10:14.057Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:10:14.058Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:11:13.977Z [LOG] Checking for scheduled streams (2025-05-30T12:11:13.976Z to 2025-05-30T12:12:13.976Z)
2025-05-30T12:11:13.979Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:11:13.980Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:11:14.059Z [LOG] Checking for scheduled streams (2025-05-30T12:11:14.059Z to 2025-05-30T12:12:14.059Z)
2025-05-30T12:11:14.062Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:11:14.064Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:12:13.955Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:12:13.957Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:12:13.959Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:12:13.960Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:12:13.986Z [LOG] Checking for scheduled streams (2025-05-30T12:12:13.985Z to 2025-05-30T12:13:13.985Z)
2025-05-30T12:12:13.989Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:12:13.989Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:12:14.062Z [LOG] Checking for scheduled streams (2025-05-30T12:12:14.061Z to 2025-05-30T12:13:14.061Z)
2025-05-30T12:12:14.064Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:12:14.065Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:13:13.992Z [LOG] Checking for scheduled streams (2025-05-30T12:13:13.991Z to 2025-05-30T12:14:13.991Z)
2025-05-30T12:13:13.994Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:13:13.995Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:13:14.068Z [LOG] Checking for scheduled streams (2025-05-30T12:13:14.068Z to 2025-05-30T12:14:14.068Z)
2025-05-30T12:13:14.071Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:13:14.072Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:14:13.993Z [LOG] Checking for scheduled streams (2025-05-30T12:14:13.992Z to 2025-05-30T12:15:13.992Z)
2025-05-30T12:14:13.998Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:14:14.016Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:14:14.076Z [LOG] Checking for scheduled streams (2025-05-30T12:14:14.075Z to 2025-05-30T12:15:14.075Z)
2025-05-30T12:14:14.078Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:14:14.079Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:15:14.003Z [LOG] Checking for scheduled streams (2025-05-30T12:15:14.003Z to 2025-05-30T12:16:14.003Z)
2025-05-30T12:15:14.005Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:15:14.007Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:15:14.084Z [LOG] Checking for scheduled streams (2025-05-30T12:15:14.083Z to 2025-05-30T12:16:14.083Z)
2025-05-30T12:15:14.087Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:15:14.089Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:16:14.011Z [LOG] Checking for scheduled streams (2025-05-30T12:16:14.011Z to 2025-05-30T12:17:14.011Z)
2025-05-30T12:16:14.014Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:16:14.015Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:16:14.090Z [LOG] Checking for scheduled streams (2025-05-30T12:16:14.089Z to 2025-05-30T12:17:14.089Z)
2025-05-30T12:16:14.092Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:16:14.095Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:17:13.960Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:17:13.961Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:17:13.963Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:17:13.963Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:17:14.021Z [LOG] Checking for scheduled streams (2025-05-30T12:17:14.020Z to 2025-05-30T12:18:14.020Z)
2025-05-30T12:17:14.024Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:17:14.025Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:17:14.098Z [LOG] Checking for scheduled streams (2025-05-30T12:17:14.098Z to 2025-05-30T12:18:14.098Z)
2025-05-30T12:17:14.101Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:17:14.101Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:18:14.025Z [LOG] Checking for scheduled streams (2025-05-30T12:18:14.025Z to 2025-05-30T12:19:14.025Z)
2025-05-30T12:18:14.028Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:18:14.029Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:18:14.115Z [LOG] Checking for scheduled streams (2025-05-30T12:18:14.113Z to 2025-05-30T12:19:14.113Z)
2025-05-30T12:18:14.118Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:18:14.119Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:19:14.040Z [LOG] Checking for scheduled streams (2025-05-30T12:19:14.039Z to 2025-05-30T12:20:14.039Z)
2025-05-30T12:19:14.043Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:19:14.044Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:19:14.124Z [LOG] Checking for scheduled streams (2025-05-30T12:19:14.122Z to 2025-05-30T12:20:14.122Z)
2025-05-30T12:19:14.127Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:19:14.129Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:20:14.050Z [LOG] Checking for scheduled streams (2025-05-30T12:20:14.050Z to 2025-05-30T12:21:14.050Z)
2025-05-30T12:20:14.053Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:20:14.054Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:20:14.124Z [LOG] Checking for scheduled streams (2025-05-30T12:20:14.123Z to 2025-05-30T12:21:14.123Z)
2025-05-30T12:20:14.127Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:20:14.129Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:21:14.058Z [LOG] Checking for scheduled streams (2025-05-30T12:21:14.058Z to 2025-05-30T12:22:14.058Z)
2025-05-30T12:21:14.061Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:21:14.062Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:21:14.126Z [LOG] Checking for scheduled streams (2025-05-30T12:21:14.126Z to 2025-05-30T12:22:14.126Z)
2025-05-30T12:21:14.129Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:21:14.130Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:22:13.970Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:22:13.972Z [LOG] [StreamingService] Found inconsistent stream e4ea3671-551a-4a7d-af09-d049aa38c001: marked as 'live' in DB but not active in memory
2025-05-30T12:22:13.974Z [LOG] [StreamingService] Updated stream e4ea3671-551a-4a7d-af09-d049aa38c001 status to 'offline'
2025-05-30T12:22:13.975Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:22:14.073Z [LOG] Checking for scheduled streams (2025-05-30T12:22:14.072Z to 2025-05-30T12:23:14.072Z)
2025-05-30T12:22:14.075Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:22:14.076Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:22:14.138Z [LOG] Checking for scheduled streams (2025-05-30T12:22:14.138Z to 2025-05-30T12:23:14.138Z)
2025-05-30T12:22:14.140Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:22:14.141Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:23:14.078Z [LOG] Checking for scheduled streams (2025-05-30T12:23:14.077Z to 2025-05-30T12:24:14.077Z)
2025-05-30T12:23:14.080Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:23:14.081Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:23:14.141Z [LOG] Checking for scheduled streams (2025-05-30T12:23:14.141Z to 2025-05-30T12:24:14.141Z)
2025-05-30T12:23:14.143Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:23:14.144Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:24:14.090Z [LOG] Checking for scheduled streams (2025-05-30T12:24:14.089Z to 2025-05-30T12:25:14.089Z)
2025-05-30T12:24:14.093Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:24:14.094Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:24:14.141Z [LOG] Checking for scheduled streams (2025-05-30T12:24:14.141Z to 2025-05-30T12:25:14.141Z)
2025-05-30T12:24:14.144Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:24:14.145Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:33:23.423Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:33:24.177Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:33:24.179Z [LOG] Stream scheduler initialized
2025-05-30T12:33:24.180Z [LOG] Checking for scheduled streams (2025-05-30T12:33:24.180Z to 2025-05-30T12:34:24.180Z)
2025-05-30T12:33:24.242Z [LOG] StreamFlow running at:
2025-05-30T12:33:24.243Z [LOG]   http://**************:7575
2025-05-30T12:33:24.244Z [LOG]   http://************:7575
2025-05-30T12:33:24.246Z [LOG]   http://*************:7575
2025-05-30T12:33:24.248Z [LOG]   http://***********:7575
2025-05-30T12:33:24.249Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:33:24.261Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:33:24.264Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:33:24.265Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:33:24.266Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:33:24.268Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.269Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.270Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.271Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.273Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.274Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.275Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.277Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.279Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.280Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.281Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.282Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.283Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:33:24.285Z [LOG] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 exceeded duration, stopping now
2025-05-30T12:33:24.286Z [LOG] [StreamingService] Stop request for stream e4ea3671-551a-4a7d-af09-d049aa38c001, isActive: false
2025-05-30T12:33:24.287Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T12:33:24.288Z [LOG] [StreamingService] Stream e4ea3671-551a-4a7d-af09-d049aa38c001 has been running for 25 minutes, attempting to restore tracking...
2025-05-30T12:33:24.291Z [LOG] [StreamingService] Video uses HEVC codec, re-encoding required for streaming compatibility
2025-05-30T12:33:24.292Z [LOG] [StreamingService] Using re-encoding mode for stream e4ea3671-551a-4a7d-af09-d049aa38c001 (video needs optimization)
2025-05-30T12:33:24.294Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748606631922-224154124.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 4000k -maxrate 4800k -bufsize 6000k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1920x1080 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/kgy2-7p0g-956d-cw1s-3jv4
2025-05-30T12:33:24.308Z [LOG] Scheduling termination for stream e4ea3671-551a-4a7d-af09-d049aa38c001 after 2 minutes
2025-05-30T12:33:24.309Z [LOG] [StreamingService] Successfully restored tracking for stream e4ea3671-551a-4a7d-af09-d049aa38c001
2025-05-30T12:33:24.310Z [LOG] Stream scheduler initialized
2025-05-30T12:33:24.312Z [LOG] Checking for scheduled streams (2025-05-30T12:33:24.311Z to 2025-05-30T12:34:24.311Z)
2025-05-30T12:33:24.314Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:33:24.317Z [LOG] [StreamingService] Stream status sync completed. Active streams: 1
2025-05-30T12:37:35.614Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:37:36.388Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:37:36.389Z [LOG] Stream scheduler initialized
2025-05-30T12:37:36.391Z [LOG] Checking for scheduled streams (2025-05-30T12:37:36.390Z to 2025-05-30T12:38:36.390Z)
2025-05-30T12:37:36.492Z [LOG] StreamFlow running at:
2025-05-30T12:37:36.493Z [LOG]   http://**************:7575
2025-05-30T12:37:36.494Z [LOG]   http://************:7575
2025-05-30T12:37:36.495Z [LOG]   http://*************:7575
2025-05-30T12:37:36.496Z [LOG]   http://***********:7575
2025-05-30T12:37:36.498Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:37:36.508Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:37:36.509Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:37:36.510Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:37:36.512Z [ERROR] Error inserting default plan: SQLITE_ERROR: no such table: subscription_plans
2025-05-30T12:37:36.514Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.518Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.522Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.524Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.525Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.527Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.529Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.532Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.533Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.535Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.537Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.541Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.544Z [ERROR] Error inserting default permission: SQLITE_ERROR: no such table: role_permissions
2025-05-30T12:37:36.548Z [ERROR] Error finding scheduled streams: SQLITE_ERROR: no such table: streams
2025-05-30T12:37:36.557Z [ERROR] Error checking scheduled streams: Error: SQLITE_ERROR: no such table: streams
--> in Database#all('\n' +
  '        SELECT s.*, \n' +
  '               v.title AS video_title, \n' +
  '               v.filepath AS video_filepath,\n' +
  '               v.thumbnail_path AS video_thumbnail, \n' +
  '               v.duration AS video_duration,\n' +
  '               v.resolution AS video_resolution,\n' +
  '               v.bitrate AS video_bitrate,\n' +
  '               v.fps AS video_fps  \n' +
  '        FROM streams s\n' +
  '        LEFT JOIN videos v ON s.video_id = v.id\n' +
  "        WHERE s.status = 'scheduled'\n" +
  '        AND s.schedule_time IS NOT NULL\n' +
  '        AND s.schedule_time >= ?\n' +
  '        AND s.schedule_time <= ?\n' +
  '      ', [ '2025-05-30T12:37:36.390Z', '2025-05-30T12:38:36.390Z' ], [Function (anonymous)])
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:244:10
    at new Promise (<anonymous>)
    at Stream.findScheduledInRange (C:\Users\<USER>\OriDrive\Desktop\streamflow\models\Stream.js:225:12)
    at checkScheduledStreams (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:22:34)
    at Object.init (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\schedulerService.js:10:3)
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:588:18)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-05-30T12:37:36.561Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T12:37:36.562Z [LOG] Stream scheduler initialized
2025-05-30T12:37:36.563Z [LOG] Checking for scheduled streams (2025-05-30T12:37:36.563Z to 2025-05-30T12:38:36.563Z)
2025-05-30T12:37:36.565Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:37:36.570Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:40:04.877Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:40:05.710Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:40:05.712Z [LOG] Stream scheduler initialized
2025-05-30T12:40:05.714Z [LOG] Checking for scheduled streams (2025-05-30T12:40:05.714Z to 2025-05-30T12:41:05.714Z)
2025-05-30T12:40:05.789Z [LOG] StreamFlow running at:
2025-05-30T12:40:05.790Z [LOG]   http://**************:7575
2025-05-30T12:40:05.791Z [LOG]   http://************:7575
2025-05-30T12:40:05.792Z [LOG]   http://*************:7575
2025-05-30T12:40:05.793Z [LOG]   http://***********:7575
2025-05-30T12:40:05.794Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:40:05.900Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T12:40:05.901Z [LOG] Stream scheduler initialized
2025-05-30T12:40:05.903Z [LOG] Checking for scheduled streams (2025-05-30T12:40:05.903Z to 2025-05-30T12:41:05.903Z)
2025-05-30T12:40:05.907Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:40:05.915Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:40:23.262Z [LOG] Validation errors: [
  {
    type: 'field',
    value: '',
    msg: 'Please enter a valid email address',
    path: 'email',
    location: 'body'
  }
]
2025-05-30T12:41:05.717Z [LOG] Checking for scheduled streams (2025-05-30T12:41:05.716Z to 2025-05-30T12:42:05.716Z)
2025-05-30T12:41:05.912Z [LOG] Checking for scheduled streams (2025-05-30T12:41:05.911Z to 2025-05-30T12:42:05.911Z)
2025-05-30T12:42:05.726Z [LOG] Checking for scheduled streams (2025-05-30T12:42:05.725Z to 2025-05-30T12:43:05.725Z)
2025-05-30T12:42:05.922Z [LOG] Checking for scheduled streams (2025-05-30T12:42:05.921Z to 2025-05-30T12:43:05.921Z)
2025-05-30T12:43:05.727Z [LOG] Checking for scheduled streams (2025-05-30T12:43:05.727Z to 2025-05-30T12:44:05.727Z)
2025-05-30T12:43:05.933Z [LOG] Checking for scheduled streams (2025-05-30T12:43:05.932Z to 2025-05-30T12:44:05.932Z)
2025-05-30T12:46:08.541Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T12:46:09.294Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T12:46:09.297Z [LOG] Stream scheduler initialized
2025-05-30T12:46:09.300Z [LOG] Checking for scheduled streams (2025-05-30T12:46:09.298Z to 2025-05-30T12:47:09.298Z)
2025-05-30T12:46:09.368Z [LOG] StreamFlow running at:
2025-05-30T12:46:09.369Z [LOG]   http://**************:7575
2025-05-30T12:46:09.370Z [LOG]   http://************:7575
2025-05-30T12:46:09.371Z [LOG]   http://*************:7575
2025-05-30T12:46:09.372Z [LOG]   http://***********:7575
2025-05-30T12:46:09.374Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T12:46:09.462Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T12:46:09.464Z [LOG] Stream scheduler initialized
2025-05-30T12:46:09.464Z [LOG] Checking for scheduled streams (2025-05-30T12:46:09.464Z to 2025-05-30T12:47:09.464Z)
2025-05-30T12:46:09.466Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:46:09.467Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:47:09.310Z [LOG] Checking for scheduled streams (2025-05-30T12:47:09.309Z to 2025-05-30T12:48:09.309Z)
2025-05-30T12:47:09.472Z [LOG] Checking for scheduled streams (2025-05-30T12:47:09.471Z to 2025-05-30T12:48:09.471Z)
2025-05-30T12:48:09.316Z [LOG] Checking for scheduled streams (2025-05-30T12:48:09.315Z to 2025-05-30T12:49:09.315Z)
2025-05-30T12:48:09.485Z [LOG] Checking for scheduled streams (2025-05-30T12:48:09.485Z to 2025-05-30T12:49:09.485Z)
2025-05-30T12:49:09.320Z [LOG] Checking for scheduled streams (2025-05-30T12:49:09.319Z to 2025-05-30T12:50:09.319Z)
2025-05-30T12:49:09.493Z [LOG] Checking for scheduled streams (2025-05-30T12:49:09.492Z to 2025-05-30T12:50:09.492Z)
2025-05-30T12:50:09.333Z [LOG] Checking for scheduled streams (2025-05-30T12:50:09.333Z to 2025-05-30T12:51:09.333Z)
2025-05-30T12:50:09.503Z [LOG] Checking for scheduled streams (2025-05-30T12:50:09.502Z to 2025-05-30T12:51:09.502Z)
2025-05-30T12:51:05.645Z [LOG] Validation errors: [
  {
    type: 'field',
    value: '',
    msg: 'Please enter a valid email address',
    path: 'email',
    location: 'body'
  }
]
2025-05-30T12:51:09.301Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:51:09.303Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:51:09.333Z [LOG] Checking for scheduled streams (2025-05-30T12:51:09.333Z to 2025-05-30T12:52:09.333Z)
2025-05-30T12:51:09.509Z [LOG] Checking for scheduled streams (2025-05-30T12:51:09.509Z to 2025-05-30T12:52:09.509Z)
2025-05-30T12:51:30.441Z [LOG] User created successfully with ID: 28fd3c08-af4b-4fe8-9b36-12e0089bd409
2025-05-30T12:51:36.658Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\subscription\plans.ejs:1
 >> 1| <%- include('../layout', { 
    2|   title: 'Subscription Plans',
    3|   content: `
    4|     <div class="min-h-screen bg-dark-900 text-white">

C:\Users\<USER>\OriDrive\Desktop\streamflow\views\layout.ejs:188
    186|       </div>
    187|       <div class="p-6 pt-20 lg:pt-22 flex-1">
 >> 188|         <%- body %>
    189|       </div>
    190|       <div class="hidden lg:flex justify-end pr-6 py-4">
    191|         <div class="flex items-center gap-2">

body is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\layout.ejs":42:17)
    at layout (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at include (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:701:39)
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\subscription\\plans.ejs":10:17)
    at plans (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
2025-05-30T12:51:42.318Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\admin\dashboard.ejs:1
 >> 1| <%- include('../layout', { 
    2|   title: 'Admin Dashboard',
    3|   content: `
    4|     <div class="min-h-screen bg-dark-900 text-white">

C:\Users\<USER>\OriDrive\Desktop\streamflow\views\layout.ejs:188
    186|       </div>
    187|       <div class="p-6 pt-20 lg:pt-22 flex-1">
 >> 188|         <%- body %>
    189|       </div>
    190|       <div class="hidden lg:flex justify-end pr-6 py-4">
    191|         <div class="flex items-center gap-2">

body is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\layout.ejs":42:17)
    at layout (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at include (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:701:39)
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\dashboard.ejs":10:17)
    at dashboard (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
2025-05-30T12:52:09.340Z [LOG] Checking for scheduled streams (2025-05-30T12:52:09.340Z to 2025-05-30T12:53:09.340Z)
2025-05-30T12:52:09.523Z [LOG] Checking for scheduled streams (2025-05-30T12:52:09.522Z to 2025-05-30T12:53:09.522Z)
2025-05-30T12:53:09.342Z [LOG] Checking for scheduled streams (2025-05-30T12:53:09.341Z to 2025-05-30T12:54:09.341Z)
2025-05-30T12:53:09.537Z [LOG] Checking for scheduled streams (2025-05-30T12:53:09.537Z to 2025-05-30T12:54:09.537Z)
2025-05-30T12:54:09.357Z [LOG] Checking for scheduled streams (2025-05-30T12:54:09.357Z to 2025-05-30T12:55:09.357Z)
2025-05-30T12:54:09.548Z [LOG] Checking for scheduled streams (2025-05-30T12:54:09.547Z to 2025-05-30T12:55:09.547Z)
2025-05-30T12:55:09.369Z [LOG] Checking for scheduled streams (2025-05-30T12:55:09.368Z to 2025-05-30T12:56:09.368Z)
2025-05-30T12:55:09.551Z [LOG] Checking for scheduled streams (2025-05-30T12:55:09.551Z to 2025-05-30T12:56:09.551Z)
2025-05-30T12:56:09.304Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T12:56:09.306Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T12:56:09.383Z [LOG] Checking for scheduled streams (2025-05-30T12:56:09.383Z to 2025-05-30T12:57:09.383Z)
2025-05-30T12:56:09.556Z [LOG] Checking for scheduled streams (2025-05-30T12:56:09.555Z to 2025-05-30T12:57:09.555Z)
2025-05-30T12:57:09.387Z [LOG] Checking for scheduled streams (2025-05-30T12:57:09.387Z to 2025-05-30T12:58:09.387Z)
2025-05-30T12:57:09.557Z [LOG] Checking for scheduled streams (2025-05-30T12:57:09.557Z to 2025-05-30T12:58:09.557Z)
2025-05-30T12:58:09.393Z [LOG] Checking for scheduled streams (2025-05-30T12:58:09.393Z to 2025-05-30T12:59:09.393Z)
2025-05-30T12:58:09.566Z [LOG] Checking for scheduled streams (2025-05-30T12:58:09.566Z to 2025-05-30T12:59:09.566Z)
2025-05-30T12:59:09.407Z [LOG] Checking for scheduled streams (2025-05-30T12:59:09.406Z to 2025-05-30T13:00:09.406Z)
2025-05-30T12:59:09.574Z [LOG] Checking for scheduled streams (2025-05-30T12:59:09.574Z to 2025-05-30T13:00:09.574Z)
2025-05-30T13:00:09.418Z [LOG] Checking for scheduled streams (2025-05-30T13:00:09.417Z to 2025-05-30T13:01:09.417Z)
2025-05-30T13:00:09.587Z [LOG] Checking for scheduled streams (2025-05-30T13:00:09.586Z to 2025-05-30T13:01:09.586Z)
2025-05-30T13:01:09.313Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:01:09.315Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:01:09.423Z [LOG] Checking for scheduled streams (2025-05-30T13:01:09.423Z to 2025-05-30T13:02:09.423Z)
2025-05-30T13:01:09.595Z [LOG] Checking for scheduled streams (2025-05-30T13:01:09.595Z to 2025-05-30T13:02:09.595Z)
2025-05-30T13:02:09.439Z [LOG] Checking for scheduled streams (2025-05-30T13:02:09.438Z to 2025-05-30T13:03:09.438Z)
2025-05-30T13:02:09.606Z [LOG] Checking for scheduled streams (2025-05-30T13:02:09.605Z to 2025-05-30T13:03:09.605Z)
2025-05-30T13:03:09.440Z [LOG] Checking for scheduled streams (2025-05-30T13:03:09.439Z to 2025-05-30T13:04:09.439Z)
2025-05-30T13:03:09.616Z [LOG] Checking for scheduled streams (2025-05-30T13:03:09.616Z to 2025-05-30T13:04:09.616Z)
2025-05-30T13:04:09.443Z [LOG] Checking for scheduled streams (2025-05-30T13:04:09.443Z to 2025-05-30T13:05:09.443Z)
2025-05-30T13:04:09.626Z [LOG] Checking for scheduled streams (2025-05-30T13:04:09.626Z to 2025-05-30T13:05:09.626Z)
2025-05-30T13:05:09.452Z [LOG] Checking for scheduled streams (2025-05-30T13:05:09.451Z to 2025-05-30T13:06:09.451Z)
2025-05-30T13:05:09.635Z [LOG] Checking for scheduled streams (2025-05-30T13:05:09.635Z to 2025-05-30T13:06:09.635Z)
2025-05-30T13:06:09.315Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:06:09.317Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:06:09.464Z [LOG] Checking for scheduled streams (2025-05-30T13:06:09.463Z to 2025-05-30T13:07:09.463Z)
2025-05-30T13:06:09.641Z [LOG] Checking for scheduled streams (2025-05-30T13:06:09.640Z to 2025-05-30T13:07:09.640Z)
2025-05-30T13:07:09.477Z [LOG] Checking for scheduled streams (2025-05-30T13:07:09.476Z to 2025-05-30T13:08:09.476Z)
2025-05-30T13:07:09.654Z [LOG] Checking for scheduled streams (2025-05-30T13:07:09.654Z to 2025-05-30T13:08:09.654Z)
2025-05-30T13:08:09.487Z [LOG] Checking for scheduled streams (2025-05-30T13:08:09.487Z to 2025-05-30T13:09:09.487Z)
2025-05-30T13:08:09.664Z [LOG] Checking for scheduled streams (2025-05-30T13:08:09.664Z to 2025-05-30T13:09:09.664Z)
2025-05-30T13:09:09.494Z [LOG] Checking for scheduled streams (2025-05-30T13:09:09.494Z to 2025-05-30T13:10:09.494Z)
2025-05-30T13:09:09.677Z [LOG] Checking for scheduled streams (2025-05-30T13:09:09.676Z to 2025-05-30T13:10:09.676Z)
2025-05-30T13:10:09.494Z [LOG] Checking for scheduled streams (2025-05-30T13:10:09.494Z to 2025-05-30T13:11:09.494Z)
2025-05-30T13:10:09.685Z [LOG] Checking for scheduled streams (2025-05-30T13:10:09.685Z to 2025-05-30T13:11:09.685Z)
2025-05-30T13:11:09.328Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:11:09.330Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:11:09.505Z [LOG] Checking for scheduled streams (2025-05-30T13:11:09.504Z to 2025-05-30T13:12:09.504Z)
2025-05-30T13:11:09.693Z [LOG] Checking for scheduled streams (2025-05-30T13:11:09.692Z to 2025-05-30T13:12:09.692Z)
2025-05-30T13:12:09.506Z [LOG] Checking for scheduled streams (2025-05-30T13:12:09.505Z to 2025-05-30T13:13:09.505Z)
2025-05-30T13:12:09.706Z [LOG] Checking for scheduled streams (2025-05-30T13:12:09.705Z to 2025-05-30T13:13:09.705Z)
2025-05-30T13:13:09.517Z [LOG] Checking for scheduled streams (2025-05-30T13:13:09.516Z to 2025-05-30T13:14:09.516Z)
2025-05-30T13:13:09.715Z [LOG] Checking for scheduled streams (2025-05-30T13:13:09.714Z to 2025-05-30T13:14:09.714Z)
2025-05-30T13:14:09.527Z [LOG] Checking for scheduled streams (2025-05-30T13:14:09.526Z to 2025-05-30T13:15:09.526Z)
2025-05-30T13:14:09.726Z [LOG] Checking for scheduled streams (2025-05-30T13:14:09.725Z to 2025-05-30T13:15:09.725Z)
2025-05-30T13:15:09.535Z [LOG] Checking for scheduled streams (2025-05-30T13:15:09.535Z to 2025-05-30T13:16:09.535Z)
2025-05-30T13:15:09.736Z [LOG] Checking for scheduled streams (2025-05-30T13:15:09.736Z to 2025-05-30T13:16:09.736Z)
2025-05-30T13:16:09.341Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:16:09.342Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:16:09.548Z [LOG] Checking for scheduled streams (2025-05-30T13:16:09.548Z to 2025-05-30T13:17:09.548Z)
2025-05-30T13:16:09.746Z [LOG] Checking for scheduled streams (2025-05-30T13:16:09.746Z to 2025-05-30T13:17:09.746Z)
2025-05-30T13:17:09.554Z [LOG] Checking for scheduled streams (2025-05-30T13:17:09.554Z to 2025-05-30T13:18:09.554Z)
2025-05-30T13:17:09.758Z [LOG] Checking for scheduled streams (2025-05-30T13:17:09.758Z to 2025-05-30T13:18:09.758Z)
2025-05-30T13:17:27.302Z [ERROR] Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\OriDrive\Desktop\streamflow\layout.ejs'
    at Object.openSync (node:fs:581:18)
    at Object.readFileSync [as fileLoader] (node:fs:457:35)
    at fileLoader (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:293:18)
    at handleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:233:16)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:16)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:353:7
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:280:5)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
2025-05-30T13:17:31.948Z [ERROR] Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\OriDrive\Desktop\streamflow\layout.ejs'
    at Object.openSync (node:fs:581:18)
    at Object.readFileSync [as fileLoader] (node:fs:457:35)
    at fileLoader (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:293:18)
    at handleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:233:16)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:16)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:353:7
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:280:5)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
2025-05-30T13:17:37.474Z [ERROR] SyntaxError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\subscription\plans.ejs:67
    65|               </div>
    66|               <% if (plan.features) { %>
 >> 67|                 <% JSON.parse(plan.features).forEach(function(feature) { %>
    68|                   <div class="flex items-center text-sm">
    69|                     <i class="ti ti-check text-green-400 mr-2"></i>
    70|                     <span class="text-gray-300"><%= feature %></span>

Unexpected non-whitespace character after JSON at position 2
    at JSON.parse (<anonymous>)
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\subscription\\plans.ejs":78:13)
    at Array.forEach (<anonymous>)
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\subscription\\plans.ejs":43:14)
    at plans (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
2025-05-30T13:21:03.244Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:21:03.934Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:21:03.935Z [LOG] Stream scheduler initialized
2025-05-30T13:21:03.936Z [LOG] Checking for scheduled streams (2025-05-30T13:21:03.936Z to 2025-05-30T13:22:03.936Z)
2025-05-30T13:21:03.990Z [LOG] StreamFlow running at:
2025-05-30T13:21:03.991Z [LOG]   http://**************:7575
2025-05-30T13:21:03.991Z [LOG]   http://************:7575
2025-05-30T13:21:03.992Z [LOG]   http://*************:7575
2025-05-30T13:21:03.992Z [LOG]   http://***********:7575
2025-05-30T13:21:03.994Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:21:04.065Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:21:04.065Z [LOG] Stream scheduler initialized
2025-05-30T13:21:04.066Z [LOG] Checking for scheduled streams (2025-05-30T13:21:04.065Z to 2025-05-30T13:22:04.065Z)
2025-05-30T13:21:04.067Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:21:04.069Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:22:03.942Z [LOG] Checking for scheduled streams (2025-05-30T13:22:03.941Z to 2025-05-30T13:23:03.941Z)
2025-05-30T13:22:04.076Z [LOG] Checking for scheduled streams (2025-05-30T13:22:04.076Z to 2025-05-30T13:23:04.076Z)
2025-05-30T13:23:03.943Z [LOG] Checking for scheduled streams (2025-05-30T13:23:03.942Z to 2025-05-30T13:24:03.942Z)
2025-05-30T13:23:04.079Z [LOG] Checking for scheduled streams (2025-05-30T13:23:04.078Z to 2025-05-30T13:24:04.078Z)
2025-05-30T13:24:03.948Z [LOG] Checking for scheduled streams (2025-05-30T13:24:03.947Z to 2025-05-30T13:25:03.947Z)
2025-05-30T13:24:04.091Z [LOG] Checking for scheduled streams (2025-05-30T13:24:04.090Z to 2025-05-30T13:25:04.090Z)
2025-05-30T13:25:03.956Z [LOG] Checking for scheduled streams (2025-05-30T13:25:03.955Z to 2025-05-30T13:26:03.955Z)
2025-05-30T13:25:04.102Z [LOG] Checking for scheduled streams (2025-05-30T13:25:04.101Z to 2025-05-30T13:26:04.101Z)
2025-05-30T13:25:23.527Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748611523286-118003451.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748611523286-118003451.mp4',
  size: 42760993
}
2025-05-30T13:25:23.948Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T13:25:24.364Z [ERROR] Error creating video: SQLITE_ERROR: table videos has no column named created_at
2025-05-30T13:25:24.366Z [ERROR] Database error: [Error: SQLITE_ERROR: table videos has no column named created_at] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:25:24.367Z [ERROR] Upload error details: [Error: SQLITE_ERROR: table videos has no column named created_at] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:26:06.369Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:26:07.042Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:26:07.044Z [LOG] Stream scheduler initialized
2025-05-30T13:26:07.045Z [LOG] Checking for scheduled streams (2025-05-30T13:26:07.045Z to 2025-05-30T13:27:07.045Z)
2025-05-30T13:26:07.106Z [LOG] StreamFlow running at:
2025-05-30T13:26:07.107Z [LOG]   http://**************:7575
2025-05-30T13:26:07.107Z [LOG]   http://************:7575
2025-05-30T13:26:07.108Z [LOG]   http://*************:7575
2025-05-30T13:26:07.108Z [LOG]   http://***********:7575
2025-05-30T13:26:07.109Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:26:07.183Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:26:07.184Z [LOG] Stream scheduler initialized
2025-05-30T13:26:07.185Z [LOG] Checking for scheduled streams (2025-05-30T13:26:07.184Z to 2025-05-30T13:27:07.184Z)
2025-05-30T13:26:07.186Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:26:07.188Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:26:33.751Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748611593483-85509875.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748611593483-85509875.mp4',
  size: 42760993
}
2025-05-30T13:26:33.872Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T13:26:34.300Z [ERROR] Error creating video: SQLITE_ERROR: table videos has no column named created_at
2025-05-30T13:26:34.302Z [ERROR] Database error: [Error: SQLITE_ERROR: table videos has no column named created_at] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:26:34.304Z [ERROR] Upload error details: [Error: SQLITE_ERROR: table videos has no column named created_at] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:27:07.047Z [LOG] Checking for scheduled streams (2025-05-30T13:27:07.047Z to 2025-05-30T13:28:07.047Z)
2025-05-30T13:27:07.192Z [LOG] Checking for scheduled streams (2025-05-30T13:27:07.191Z to 2025-05-30T13:28:07.191Z)
2025-05-30T13:27:28.646Z [ERROR] Error: Failed to lookup view "admin/plans" in views directory "C:\Users\<USER>\OriDrive\Desktop\streamflow\views"
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:597:17)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:121:9
2025-05-30T13:27:45.753Z [ERROR] Error: Failed to lookup view "admin/users" in views directory "C:\Users\<USER>\OriDrive\Desktop\streamflow\views"
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:597:17)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:48:9
2025-05-30T13:28:07.060Z [LOG] Checking for scheduled streams (2025-05-30T13:28:07.060Z to 2025-05-30T13:29:07.060Z)
2025-05-30T13:28:07.203Z [LOG] Checking for scheduled streams (2025-05-30T13:28:07.202Z to 2025-05-30T13:29:07.202Z)
2025-05-30T13:29:07.061Z [LOG] Checking for scheduled streams (2025-05-30T13:29:07.060Z to 2025-05-30T13:30:07.060Z)
2025-05-30T13:29:07.206Z [LOG] Checking for scheduled streams (2025-05-30T13:29:07.206Z to 2025-05-30T13:30:07.206Z)
2025-05-30T13:30:07.074Z [LOG] Checking for scheduled streams (2025-05-30T13:30:07.074Z to 2025-05-30T13:31:07.074Z)
2025-05-30T13:30:07.215Z [LOG] Checking for scheduled streams (2025-05-30T13:30:07.215Z to 2025-05-30T13:31:07.215Z)
2025-05-30T13:30:29.715Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:30:30.419Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:30:30.421Z [LOG] Stream scheduler initialized
2025-05-30T13:30:30.422Z [LOG] Checking for scheduled streams (2025-05-30T13:30:30.422Z to 2025-05-30T13:31:30.422Z)
2025-05-30T13:30:30.435Z [LOG] Checking database schema...
2025-05-30T13:30:30.485Z [LOG] StreamFlow running at:
2025-05-30T13:30:30.485Z [LOG]   http://**************:7575
2025-05-30T13:30:30.486Z [LOG]   http://************:7575
2025-05-30T13:30:30.487Z [LOG]   http://*************:7575
2025-05-30T13:30:30.487Z [LOG]   http://***********:7575
2025-05-30T13:30:30.488Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:30:30.600Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:30:30.600Z [LOG] Stream scheduler initialized
2025-05-30T13:30:30.601Z [LOG] Checking for scheduled streams (2025-05-30T13:30:30.601Z to 2025-05-30T13:31:30.601Z)
2025-05-30T13:30:30.603Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:30:30.605Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:30:31.508Z [LOG] ✅ Database migration completed
2025-05-30T13:30:31.509Z [LOG] Database migration completed successfully
2025-05-30T13:31:18.854Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748611878594-674066304.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748611878594-674066304.mp4',
  size: 42760993
}
2025-05-30T13:31:19.082Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T13:31:30.431Z [LOG] Checking for scheduled streams (2025-05-30T13:31:30.430Z to 2025-05-30T13:32:30.430Z)
2025-05-30T13:31:30.610Z [LOG] Checking for scheduled streams (2025-05-30T13:31:30.609Z to 2025-05-30T13:32:30.609Z)
2025-05-30T13:31:44.684Z [ERROR] Error creating stream: SQLITE_ERROR: table streams has no column named orientation
2025-05-30T13:31:44.686Z [ERROR] Error creating stream: [Error: SQLITE_ERROR: table streams has no column named orientation] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T13:32:08.602Z [ERROR] Error: Failed to lookup view "admin/users" in views directory "C:\Users\<USER>\OriDrive\Desktop\streamflow\views"
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:597:17)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:48:9
2025-05-30T13:32:11.159Z [ERROR] Error: Failed to lookup view "admin/plans" in views directory "C:\Users\<USER>\OriDrive\Desktop\streamflow\views"
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:597:17)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:121:9
2025-05-30T13:32:30.443Z [LOG] Checking for scheduled streams (2025-05-30T13:32:30.443Z to 2025-05-30T13:33:30.443Z)
2025-05-30T13:32:30.618Z [LOG] Checking for scheduled streams (2025-05-30T13:32:30.618Z to 2025-05-30T13:33:30.618Z)
2025-05-30T13:33:30.447Z [LOG] Checking for scheduled streams (2025-05-30T13:33:30.447Z to 2025-05-30T13:34:30.447Z)
2025-05-30T13:33:30.631Z [LOG] Checking for scheduled streams (2025-05-30T13:33:30.631Z to 2025-05-30T13:34:30.631Z)
2025-05-30T13:36:45.390Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:36:46.080Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:36:46.082Z [LOG] Stream scheduler initialized
2025-05-30T13:36:46.083Z [LOG] Checking for scheduled streams (2025-05-30T13:36:46.083Z to 2025-05-30T13:37:46.083Z)
2025-05-30T13:36:46.100Z [LOG] Checking database schema...
2025-05-30T13:36:46.147Z [LOG] StreamFlow running at:
2025-05-30T13:36:46.147Z [LOG]   http://**************:7575
2025-05-30T13:36:46.148Z [LOG]   http://************:7575
2025-05-30T13:36:46.149Z [LOG]   http://*************:7575
2025-05-30T13:36:46.149Z [LOG]   http://***********:7575
2025-05-30T13:36:46.150Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:36:46.233Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:36:46.234Z [LOG] Stream scheduler initialized
2025-05-30T13:36:46.235Z [LOG] Checking for scheduled streams (2025-05-30T13:36:46.235Z to 2025-05-30T13:37:46.235Z)
2025-05-30T13:36:46.238Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:36:46.239Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:36:47.170Z [LOG] ✅ Database migration completed
2025-05-30T13:36:47.172Z [LOG] Database migration completed successfully
2025-05-30T13:37:46.094Z [LOG] Checking for scheduled streams (2025-05-30T13:37:46.094Z to 2025-05-30T13:38:46.094Z)
2025-05-30T13:37:46.251Z [LOG] Checking for scheduled streams (2025-05-30T13:37:46.251Z to 2025-05-30T13:38:46.251Z)
2025-05-30T13:38:26.284Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\admin\users.ejs:25
    23|       <div>
    24|         <p class="text-gray-400 text-sm">Total Users</p>
 >> 25|         <p class="text-2xl font-bold text-white"><%= stats.total_users || 0 %></p>
    26|       </div>
    27|       <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
    28|         <i class="ti ti-users text-blue-400 text-xl"></i>

stats is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\users.ejs":13:26)
    at users (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:609:3)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:48:9
2025-05-30T13:49:50.541Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T13:49:51.235Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T13:49:51.237Z [LOG] Stream scheduler initialized
2025-05-30T13:49:51.238Z [LOG] Checking for scheduled streams (2025-05-30T13:49:51.238Z to 2025-05-30T13:50:51.238Z)
2025-05-30T13:49:51.251Z [LOG] Checking database schema...
2025-05-30T13:49:51.295Z [LOG] StreamFlow running at:
2025-05-30T13:49:51.297Z [LOG]   http://**************:7575
2025-05-30T13:49:51.298Z [LOG]   http://************:7575
2025-05-30T13:49:51.299Z [LOG]   http://*************:7575
2025-05-30T13:49:51.299Z [LOG]   http://***********:7575
2025-05-30T13:49:51.300Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T13:49:51.377Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T13:49:51.378Z [LOG] Stream scheduler initialized
2025-05-30T13:49:51.379Z [LOG] Checking for scheduled streams (2025-05-30T13:49:51.379Z to 2025-05-30T13:50:51.379Z)
2025-05-30T13:49:51.381Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:49:51.383Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:49:52.330Z [LOG] ✅ Database migration completed
2025-05-30T13:49:52.331Z [LOG] Database migration completed successfully
2025-05-30T13:50:51.264Z [LOG] Checking for scheduled streams (2025-05-30T13:50:51.264Z to 2025-05-30T13:51:51.264Z)
2025-05-30T13:50:51.398Z [LOG] Checking for scheduled streams (2025-05-30T13:50:51.398Z to 2025-05-30T13:51:51.398Z)
2025-05-30T13:51:51.275Z [LOG] Checking for scheduled streams (2025-05-30T13:51:51.275Z to 2025-05-30T13:52:51.275Z)
2025-05-30T13:51:51.403Z [LOG] Checking for scheduled streams (2025-05-30T13:51:51.402Z to 2025-05-30T13:52:51.402Z)
2025-05-30T13:52:51.280Z [LOG] Checking for scheduled streams (2025-05-30T13:52:51.279Z to 2025-05-30T13:53:51.279Z)
2025-05-30T13:52:51.405Z [LOG] Checking for scheduled streams (2025-05-30T13:52:51.404Z to 2025-05-30T13:53:51.404Z)
2025-05-30T13:53:05.389Z [ERROR] ReferenceError: C:\Users\<USER>\OriDrive\Desktop\streamflow\views\admin\users.ejs:44
    42|       <div>
    43|         <p class="text-gray-400 text-sm">Total Users</p>
 >> 44|         <p class="text-2xl font-bold text-white"><%= stats.total_users || 0 %></p>
    45|       </div>
    46|       <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
    47|         <i class="ti ti-users text-blue-400 text-xl"></i>

stats is not defined
    at eval ("C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\views\\admin\\users.ejs":24:26)
    at users (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:703:17)
    at tryHandleCache (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:274:36)
    at exports.renderFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs\lib\ejs.js:491:10)
    at View.renderFile [as engine] (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\ejs-mate\lib\index.js:298:7)
    at View.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\view.js:135:8)
    at tryRender (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:657:10)
    at Function.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\application.js:609:3)
    at ServerResponse.render (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\express\lib\response.js:1049:7)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\routes\admin.js:48:9
2025-05-30T13:53:51.293Z [LOG] Checking for scheduled streams (2025-05-30T13:53:51.292Z to 2025-05-30T13:54:51.292Z)
2025-05-30T13:53:51.414Z [LOG] Checking for scheduled streams (2025-05-30T13:53:51.414Z to 2025-05-30T13:54:51.414Z)
2025-05-30T13:54:51.253Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:54:51.254Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:54:51.295Z [LOG] Checking for scheduled streams (2025-05-30T13:54:51.295Z to 2025-05-30T13:55:51.295Z)
2025-05-30T13:54:51.414Z [LOG] Checking for scheduled streams (2025-05-30T13:54:51.414Z to 2025-05-30T13:55:51.414Z)
2025-05-30T13:55:51.301Z [LOG] Checking for scheduled streams (2025-05-30T13:55:51.301Z to 2025-05-30T13:56:51.301Z)
2025-05-30T13:55:51.417Z [LOG] Checking for scheduled streams (2025-05-30T13:55:51.417Z to 2025-05-30T13:56:51.417Z)
2025-05-30T13:56:51.310Z [LOG] Checking for scheduled streams (2025-05-30T13:56:51.310Z to 2025-05-30T13:57:51.310Z)
2025-05-30T13:56:51.424Z [LOG] Checking for scheduled streams (2025-05-30T13:56:51.424Z to 2025-05-30T13:57:51.424Z)
2025-05-30T13:57:51.325Z [LOG] Checking for scheduled streams (2025-05-30T13:57:51.324Z to 2025-05-30T13:58:51.324Z)
2025-05-30T13:57:51.437Z [LOG] Checking for scheduled streams (2025-05-30T13:57:51.436Z to 2025-05-30T13:58:51.436Z)
2025-05-30T13:58:51.334Z [LOG] Checking for scheduled streams (2025-05-30T13:58:51.334Z to 2025-05-30T13:59:51.334Z)
2025-05-30T13:58:51.448Z [LOG] Checking for scheduled streams (2025-05-30T13:58:51.447Z to 2025-05-30T13:59:51.447Z)
2025-05-30T13:59:51.267Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T13:59:51.269Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T13:59:51.341Z [LOG] Checking for scheduled streams (2025-05-30T13:59:51.341Z to 2025-05-30T14:00:51.341Z)
2025-05-30T13:59:51.458Z [LOG] Checking for scheduled streams (2025-05-30T13:59:51.457Z to 2025-05-30T14:00:51.457Z)
2025-05-30T14:01:24.662Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T14:01:25.336Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T14:01:25.338Z [LOG] Stream scheduler initialized
2025-05-30T14:01:25.339Z [LOG] Checking for scheduled streams (2025-05-30T14:01:25.339Z to 2025-05-30T14:02:25.339Z)
2025-05-30T14:01:25.350Z [LOG] Checking database schema...
2025-05-30T14:01:25.394Z [LOG] StreamFlow running at:
2025-05-30T14:01:25.395Z [LOG]   http://**************:7575
2025-05-30T14:01:25.396Z [LOG]   http://************:7575
2025-05-30T14:01:25.396Z [LOG]   http://*************:7575
2025-05-30T14:01:25.397Z [LOG]   http://***********:7575
2025-05-30T14:01:25.398Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T14:01:25.482Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T14:01:25.482Z [LOG] Stream scheduler initialized
2025-05-30T14:01:25.483Z [LOG] Checking for scheduled streams (2025-05-30T14:01:25.483Z to 2025-05-30T14:02:25.483Z)
2025-05-30T14:01:25.486Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:01:25.488Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:01:26.419Z [LOG] ✅ Database migration completed
2025-05-30T14:01:26.421Z [LOG] Database migration completed successfully
2025-05-30T14:02:25.362Z [LOG] Checking for scheduled streams (2025-05-30T14:02:25.362Z to 2025-05-30T14:03:25.362Z)
2025-05-30T14:02:25.508Z [LOG] Checking for scheduled streams (2025-05-30T14:02:25.507Z to 2025-05-30T14:03:25.507Z)
2025-05-30T14:03:25.368Z [LOG] Checking for scheduled streams (2025-05-30T14:03:25.368Z to 2025-05-30T14:04:25.368Z)
2025-05-30T14:03:25.514Z [LOG] Checking for scheduled streams (2025-05-30T14:03:25.514Z to 2025-05-30T14:04:25.514Z)
2025-05-30T14:04:25.369Z [LOG] Checking for scheduled streams (2025-05-30T14:04:25.369Z to 2025-05-30T14:05:25.369Z)
2025-05-30T14:04:25.522Z [LOG] Checking for scheduled streams (2025-05-30T14:04:25.521Z to 2025-05-30T14:05:25.521Z)
2025-05-30T14:05:25.372Z [LOG] Checking for scheduled streams (2025-05-30T14:05:25.371Z to 2025-05-30T14:06:25.371Z)
2025-05-30T14:05:25.524Z [LOG] Checking for scheduled streams (2025-05-30T14:05:25.523Z to 2025-05-30T14:06:25.523Z)
2025-05-30T14:06:25.356Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:06:25.358Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:06:25.372Z [LOG] Checking for scheduled streams (2025-05-30T14:06:25.371Z to 2025-05-30T14:07:25.371Z)
2025-05-30T14:06:25.529Z [LOG] Checking for scheduled streams (2025-05-30T14:06:25.529Z to 2025-05-30T14:07:25.529Z)
2025-05-30T14:07:25.396Z [LOG] Checking for scheduled streams (2025-05-30T14:07:25.392Z to 2025-05-30T14:08:25.392Z)
2025-05-30T14:07:25.529Z [LOG] Checking for scheduled streams (2025-05-30T14:07:25.528Z to 2025-05-30T14:08:25.528Z)
2025-05-30T14:08:25.401Z [LOG] Checking for scheduled streams (2025-05-30T14:08:25.400Z to 2025-05-30T14:09:25.400Z)
2025-05-30T14:08:25.535Z [LOG] Checking for scheduled streams (2025-05-30T14:08:25.534Z to 2025-05-30T14:09:25.534Z)
2025-05-30T14:09:25.402Z [LOG] Checking for scheduled streams (2025-05-30T14:09:25.402Z to 2025-05-30T14:10:25.402Z)
2025-05-30T14:09:25.546Z [LOG] Checking for scheduled streams (2025-05-30T14:09:25.545Z to 2025-05-30T14:10:25.545Z)
2025-05-30T14:10:25.412Z [LOG] Checking for scheduled streams (2025-05-30T14:10:25.412Z to 2025-05-30T14:11:25.412Z)
2025-05-30T14:10:25.556Z [LOG] Checking for scheduled streams (2025-05-30T14:10:25.555Z to 2025-05-30T14:11:25.555Z)
2025-05-30T14:11:25.362Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:11:25.364Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:11:25.444Z [LOG] Checking for scheduled streams (2025-05-30T14:11:25.432Z to 2025-05-30T14:12:25.432Z)
2025-05-30T14:11:25.558Z [LOG] Checking for scheduled streams (2025-05-30T14:11:25.557Z to 2025-05-30T14:12:25.557Z)
2025-05-30T14:12:52.282Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T14:12:52.998Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T14:12:53.000Z [LOG] Stream scheduler initialized
2025-05-30T14:12:53.001Z [LOG] Checking for scheduled streams (2025-05-30T14:12:53.001Z to 2025-05-30T14:13:53.001Z)
2025-05-30T14:12:53.018Z [LOG] Checking database schema...
2025-05-30T14:12:53.065Z [LOG] StreamFlow running at:
2025-05-30T14:12:53.066Z [LOG]   http://**************:7575
2025-05-30T14:12:53.066Z [LOG]   http://************:7575
2025-05-30T14:12:53.067Z [LOG]   http://*************:7575
2025-05-30T14:12:53.068Z [LOG]   http://***********:7575
2025-05-30T14:12:53.069Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T14:12:53.148Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T14:12:53.148Z [LOG] Stream scheduler initialized
2025-05-30T14:12:53.149Z [LOG] Checking for scheduled streams (2025-05-30T14:12:53.149Z to 2025-05-30T14:13:53.149Z)
2025-05-30T14:12:53.150Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:12:53.152Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:12:54.090Z [LOG] ✅ Database migration completed
2025-05-30T14:12:54.091Z [LOG] Database migration completed successfully
2025-05-30T14:13:53.003Z [LOG] Checking for scheduled streams (2025-05-30T14:13:53.002Z to 2025-05-30T14:14:53.002Z)
2025-05-30T14:13:53.162Z [LOG] Checking for scheduled streams (2025-05-30T14:13:53.162Z to 2025-05-30T14:14:53.162Z)
2025-05-30T14:27:34.821Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T14:27:35.482Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T14:27:35.485Z [LOG] Stream scheduler initialized
2025-05-30T14:27:35.486Z [LOG] Checking for scheduled streams (2025-05-30T14:27:35.486Z to 2025-05-30T14:28:35.486Z)
2025-05-30T14:27:35.498Z [LOG] Checking database schema...
2025-05-30T14:27:35.548Z [LOG] StreamFlow running at:
2025-05-30T14:27:35.549Z [LOG]   http://**************:7575
2025-05-30T14:27:35.550Z [LOG]   http://************:7575
2025-05-30T14:27:35.551Z [LOG]   http://*************:7575
2025-05-30T14:27:35.552Z [LOG]   http://***********:7575
2025-05-30T14:27:35.553Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T14:27:35.619Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T14:27:35.620Z [LOG] Stream scheduler initialized
2025-05-30T14:27:35.620Z [LOG] Checking for scheduled streams (2025-05-30T14:27:35.620Z to 2025-05-30T14:28:35.620Z)
2025-05-30T14:27:35.622Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:27:35.624Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:27:36.571Z [LOG] ✅ Database migration completed
2025-05-30T14:27:36.573Z [LOG] Database migration completed successfully
2025-05-30T14:28:35.495Z [LOG] Checking for scheduled streams (2025-05-30T14:28:35.494Z to 2025-05-30T14:29:35.494Z)
2025-05-30T14:28:35.630Z [LOG] Checking for scheduled streams (2025-05-30T14:28:35.629Z to 2025-05-30T14:29:35.629Z)
2025-05-30T14:29:35.508Z [LOG] Checking for scheduled streams (2025-05-30T14:29:35.507Z to 2025-05-30T14:30:35.507Z)
2025-05-30T14:29:35.645Z [LOG] Checking for scheduled streams (2025-05-30T14:29:35.644Z to 2025-05-30T14:30:35.644Z)
2025-05-30T14:30:35.508Z [LOG] Checking for scheduled streams (2025-05-30T14:30:35.508Z to 2025-05-30T14:31:35.508Z)
2025-05-30T14:30:35.661Z [LOG] Checking for scheduled streams (2025-05-30T14:30:35.660Z to 2025-05-30T14:31:35.660Z)
2025-05-30T14:31:35.512Z [LOG] Checking for scheduled streams (2025-05-30T14:31:35.512Z to 2025-05-30T14:32:35.512Z)
2025-05-30T14:31:35.665Z [LOG] Checking for scheduled streams (2025-05-30T14:31:35.664Z to 2025-05-30T14:32:35.664Z)
2025-05-30T14:32:35.507Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:32:35.510Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:32:35.522Z [LOG] Checking for scheduled streams (2025-05-30T14:32:35.522Z to 2025-05-30T14:33:35.522Z)
2025-05-30T14:32:35.676Z [LOG] Checking for scheduled streams (2025-05-30T14:32:35.675Z to 2025-05-30T14:33:35.675Z)
2025-05-30T14:33:35.537Z [LOG] Checking for scheduled streams (2025-05-30T14:33:35.537Z to 2025-05-30T14:34:35.537Z)
2025-05-30T14:33:35.676Z [LOG] Checking for scheduled streams (2025-05-30T14:33:35.676Z to 2025-05-30T14:34:35.676Z)
2025-05-30T14:34:35.545Z [LOG] Checking for scheduled streams (2025-05-30T14:34:35.544Z to 2025-05-30T14:35:35.544Z)
2025-05-30T14:34:35.677Z [LOG] Checking for scheduled streams (2025-05-30T14:34:35.677Z to 2025-05-30T14:35:35.677Z)
2025-05-30T14:35:35.557Z [LOG] Checking for scheduled streams (2025-05-30T14:35:35.556Z to 2025-05-30T14:36:35.556Z)
2025-05-30T14:35:35.690Z [LOG] Checking for scheduled streams (2025-05-30T14:35:35.687Z to 2025-05-30T14:36:35.687Z)
2025-05-30T14:36:35.563Z [LOG] Checking for scheduled streams (2025-05-30T14:36:35.562Z to 2025-05-30T14:37:35.562Z)
2025-05-30T14:36:35.692Z [LOG] Checking for scheduled streams (2025-05-30T14:36:35.692Z to 2025-05-30T14:37:35.692Z)
2025-05-30T14:38:34.820Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T14:38:35.706Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T14:38:35.708Z [LOG] Stream scheduler initialized
2025-05-30T14:38:35.709Z [LOG] Checking for scheduled streams (2025-05-30T14:38:35.709Z to 2025-05-30T14:39:35.709Z)
2025-05-30T14:38:35.722Z [LOG] Checking database schema...
2025-05-30T14:38:35.788Z [LOG] StreamFlow running at:
2025-05-30T14:38:35.789Z [LOG]   http://**************:7575
2025-05-30T14:38:35.790Z [LOG]   http://************:7575
2025-05-30T14:38:35.791Z [LOG]   http://*************:7575
2025-05-30T14:38:35.793Z [LOG]   http://***********:7575
2025-05-30T14:38:35.794Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T14:38:35.867Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T14:38:35.868Z [LOG] Stream scheduler initialized
2025-05-30T14:38:35.869Z [LOG] Checking for scheduled streams (2025-05-30T14:38:35.869Z to 2025-05-30T14:39:35.869Z)
2025-05-30T14:38:35.871Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:38:35.873Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:38:36.817Z [LOG] ✅ Database migration completed
2025-05-30T14:38:36.818Z [LOG] Database migration completed successfully
2025-05-30T14:39:35.709Z [LOG] Checking for scheduled streams (2025-05-30T14:39:35.708Z to 2025-05-30T14:40:35.708Z)
2025-05-30T14:39:35.874Z [LOG] Checking for scheduled streams (2025-05-30T14:39:35.873Z to 2025-05-30T14:40:35.873Z)
2025-05-30T14:40:35.718Z [LOG] Checking for scheduled streams (2025-05-30T14:40:35.718Z to 2025-05-30T14:41:35.718Z)
2025-05-30T14:40:35.891Z [LOG] Checking for scheduled streams (2025-05-30T14:40:35.890Z to 2025-05-30T14:41:35.890Z)
2025-05-30T14:41:35.725Z [LOG] Checking for scheduled streams (2025-05-30T14:41:35.725Z to 2025-05-30T14:42:35.725Z)
2025-05-30T14:41:35.901Z [LOG] Checking for scheduled streams (2025-05-30T14:41:35.900Z to 2025-05-30T14:42:35.900Z)
2025-05-30T14:42:35.743Z [LOG] Checking for scheduled streams (2025-05-30T14:42:35.731Z to 2025-05-30T14:43:35.731Z)
2025-05-30T14:42:35.907Z [LOG] Checking for scheduled streams (2025-05-30T14:42:35.907Z to 2025-05-30T14:43:35.907Z)
2025-05-30T14:43:35.714Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:43:35.716Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:43:35.734Z [LOG] Checking for scheduled streams (2025-05-30T14:43:35.734Z to 2025-05-30T14:44:35.734Z)
2025-05-30T14:43:35.916Z [LOG] Checking for scheduled streams (2025-05-30T14:43:35.916Z to 2025-05-30T14:44:35.916Z)
2025-05-30T14:44:35.742Z [LOG] Checking for scheduled streams (2025-05-30T14:44:35.741Z to 2025-05-30T14:45:35.741Z)
2025-05-30T14:44:35.928Z [LOG] Checking for scheduled streams (2025-05-30T14:44:35.928Z to 2025-05-30T14:45:35.928Z)
2025-05-30T14:45:35.741Z [LOG] Checking for scheduled streams (2025-05-30T14:45:35.741Z to 2025-05-30T14:46:35.741Z)
2025-05-30T14:45:35.938Z [LOG] Checking for scheduled streams (2025-05-30T14:45:35.937Z to 2025-05-30T14:46:35.937Z)
2025-05-30T14:46:35.752Z [LOG] Checking for scheduled streams (2025-05-30T14:46:35.752Z to 2025-05-30T14:47:35.752Z)
2025-05-30T14:46:35.947Z [LOG] Checking for scheduled streams (2025-05-30T14:46:35.947Z to 2025-05-30T14:47:35.947Z)
2025-05-30T14:47:35.766Z [LOG] Checking for scheduled streams (2025-05-30T14:47:35.765Z to 2025-05-30T14:48:35.765Z)
2025-05-30T14:47:35.958Z [LOG] Checking for scheduled streams (2025-05-30T14:47:35.958Z to 2025-05-30T14:48:35.958Z)
2025-05-30T14:48:35.723Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:48:35.725Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:48:35.827Z [LOG] Checking for scheduled streams (2025-05-30T14:48:35.799Z to 2025-05-30T14:49:35.799Z)
2025-05-30T14:48:35.970Z [LOG] Checking for scheduled streams (2025-05-30T14:48:35.969Z to 2025-05-30T14:49:35.969Z)
2025-05-30T14:49:35.801Z [LOG] Checking for scheduled streams (2025-05-30T14:49:35.800Z to 2025-05-30T14:50:35.800Z)
2025-05-30T14:49:35.977Z [LOG] Checking for scheduled streams (2025-05-30T14:49:35.977Z to 2025-05-30T14:50:35.977Z)
2025-05-30T14:50:35.810Z [LOG] Checking for scheduled streams (2025-05-30T14:50:35.810Z to 2025-05-30T14:51:35.810Z)
2025-05-30T14:50:35.986Z [LOG] Checking for scheduled streams (2025-05-30T14:50:35.986Z to 2025-05-30T14:51:35.986Z)
2025-05-30T14:51:35.815Z [LOG] Checking for scheduled streams (2025-05-30T14:51:35.814Z to 2025-05-30T14:52:35.814Z)
2025-05-30T14:51:35.985Z [LOG] Checking for scheduled streams (2025-05-30T14:51:35.985Z to 2025-05-30T14:52:35.985Z)
2025-05-30T14:52:35.817Z [LOG] Checking for scheduled streams (2025-05-30T14:52:35.816Z to 2025-05-30T14:53:35.816Z)
2025-05-30T14:52:35.992Z [LOG] Checking for scheduled streams (2025-05-30T14:52:35.992Z to 2025-05-30T14:53:35.992Z)
2025-05-30T14:53:35.726Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:53:35.728Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:53:35.819Z [LOG] Checking for scheduled streams (2025-05-30T14:53:35.819Z to 2025-05-30T14:54:35.819Z)
2025-05-30T14:53:35.998Z [LOG] Checking for scheduled streams (2025-05-30T14:53:35.997Z to 2025-05-30T14:54:35.997Z)
2025-05-30T14:54:35.830Z [LOG] Checking for scheduled streams (2025-05-30T14:54:35.829Z to 2025-05-30T14:55:35.829Z)
2025-05-30T14:54:35.999Z [LOG] Checking for scheduled streams (2025-05-30T14:54:35.999Z to 2025-05-30T14:55:35.999Z)
2025-05-30T14:57:34.670Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T14:57:35.347Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T14:57:35.351Z [LOG] Stream scheduler initialized
2025-05-30T14:57:35.354Z [LOG] Checking for scheduled streams (2025-05-30T14:57:35.353Z to 2025-05-30T14:58:35.353Z)
2025-05-30T14:57:35.402Z [LOG] Checking database schema...
2025-05-30T14:57:35.478Z [LOG] StreamFlow running at:
2025-05-30T14:57:35.479Z [LOG]   http://**************:7575
2025-05-30T14:57:35.479Z [LOG]   http://************:7575
2025-05-30T14:57:35.480Z [LOG]   http://*************:7575
2025-05-30T14:57:35.481Z [LOG]   http://***********:7575
2025-05-30T14:57:35.482Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T14:57:35.551Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T14:57:35.552Z [LOG] Stream scheduler initialized
2025-05-30T14:57:35.553Z [LOG] Checking for scheduled streams (2025-05-30T14:57:35.552Z to 2025-05-30T14:58:35.552Z)
2025-05-30T14:57:35.554Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T14:57:35.556Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T14:57:36.560Z [LOG] ✅ Database migration completed
2025-05-30T14:57:36.562Z [LOG] Database migration completed successfully
2025-05-30T14:58:35.362Z [LOG] Checking for scheduled streams (2025-05-30T14:58:35.361Z to 2025-05-30T14:59:35.361Z)
2025-05-30T14:58:35.569Z [LOG] Checking for scheduled streams (2025-05-30T14:58:35.568Z to 2025-05-30T14:59:35.568Z)
2025-05-30T14:59:35.367Z [LOG] Checking for scheduled streams (2025-05-30T14:59:35.366Z to 2025-05-30T15:00:35.366Z)
2025-05-30T14:59:35.578Z [LOG] Checking for scheduled streams (2025-05-30T14:59:35.578Z to 2025-05-30T15:00:35.578Z)
2025-05-30T15:00:35.369Z [LOG] Checking for scheduled streams (2025-05-30T15:00:35.368Z to 2025-05-30T15:01:35.368Z)
2025-05-30T15:00:35.590Z [LOG] Checking for scheduled streams (2025-05-30T15:00:35.590Z to 2025-05-30T15:01:35.590Z)
2025-05-30T15:01:35.371Z [LOG] Checking for scheduled streams (2025-05-30T15:01:35.371Z to 2025-05-30T15:02:35.371Z)
2025-05-30T15:01:35.600Z [LOG] Checking for scheduled streams (2025-05-30T15:01:35.599Z to 2025-05-30T15:02:35.599Z)
2025-05-30T15:02:32.666Z [ERROR] Error creating stream: SQLITE_ERROR: table streams has no column named loop_video
2025-05-30T15:02:32.669Z [ERROR] Error creating stream: [Error: SQLITE_ERROR: table streams has no column named loop_video] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T15:02:35.357Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:02:35.359Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:02:35.375Z [LOG] Checking for scheduled streams (2025-05-30T15:02:35.375Z to 2025-05-30T15:03:35.375Z)
2025-05-30T15:02:35.607Z [LOG] Checking for scheduled streams (2025-05-30T15:02:35.607Z to 2025-05-30T15:03:35.607Z)
2025-05-30T15:02:42.627Z [ERROR] Error creating stream: SQLITE_ERROR: table streams has no column named loop_video
2025-05-30T15:02:42.628Z [ERROR] Error creating stream: [Error: SQLITE_ERROR: table streams has no column named loop_video] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T15:02:48.234Z [ERROR] Error creating stream: SQLITE_ERROR: table streams has no column named loop_video
2025-05-30T15:02:48.235Z [ERROR] Error creating stream: [Error: SQLITE_ERROR: table streams has no column named loop_video] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T15:12:07.774Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T15:12:08.521Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T15:12:08.523Z [LOG] Stream scheduler initialized
2025-05-30T15:12:08.524Z [LOG] Checking for scheduled streams (2025-05-30T15:12:08.524Z to 2025-05-30T15:13:08.524Z)
2025-05-30T15:12:08.536Z [LOG] Checking database schema...
2025-05-30T15:12:08.588Z [LOG] StreamFlow running at:
2025-05-30T15:12:08.589Z [LOG]   http://**************:7575
2025-05-30T15:12:08.590Z [LOG]   http://************:7575
2025-05-30T15:12:08.591Z [LOG]   http://*************:7575
2025-05-30T15:12:08.592Z [LOG]   http://***********:7575
2025-05-30T15:12:08.593Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T15:12:08.653Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T15:12:08.653Z [LOG] Stream scheduler initialized
2025-05-30T15:12:08.654Z [LOG] Checking for scheduled streams (2025-05-30T15:12:08.654Z to 2025-05-30T15:13:08.654Z)
2025-05-30T15:12:08.655Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:12:08.657Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:12:09.608Z [LOG] ✅ Database migration completed
2025-05-30T15:12:09.610Z [LOG] Database migration completed successfully
2025-05-30T15:13:08.529Z [LOG] Checking for scheduled streams (2025-05-30T15:13:08.529Z to 2025-05-30T15:14:08.529Z)
2025-05-30T15:13:08.663Z [LOG] Checking for scheduled streams (2025-05-30T15:13:08.663Z to 2025-05-30T15:14:08.663Z)
2025-05-30T15:14:08.536Z [LOG] Checking for scheduled streams (2025-05-30T15:14:08.536Z to 2025-05-30T15:15:08.536Z)
2025-05-30T15:14:08.672Z [LOG] Checking for scheduled streams (2025-05-30T15:14:08.671Z to 2025-05-30T15:15:08.671Z)
2025-05-30T15:15:08.550Z [LOG] Checking for scheduled streams (2025-05-30T15:15:08.549Z to 2025-05-30T15:16:08.549Z)
2025-05-30T15:15:08.685Z [LOG] Checking for scheduled streams (2025-05-30T15:15:08.685Z to 2025-05-30T15:16:08.685Z)
2025-05-30T15:16:08.561Z [LOG] Checking for scheduled streams (2025-05-30T15:16:08.560Z to 2025-05-30T15:17:08.560Z)
2025-05-30T15:16:08.695Z [LOG] Checking for scheduled streams (2025-05-30T15:16:08.694Z to 2025-05-30T15:17:08.694Z)
2025-05-30T15:17:08.533Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:17:08.535Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:17:08.570Z [LOG] Checking for scheduled streams (2025-05-30T15:17:08.570Z to 2025-05-30T15:18:08.570Z)
2025-05-30T15:17:08.696Z [LOG] Checking for scheduled streams (2025-05-30T15:17:08.696Z to 2025-05-30T15:18:08.696Z)
2025-05-30T15:18:08.582Z [LOG] Checking for scheduled streams (2025-05-30T15:18:08.582Z to 2025-05-30T15:19:08.582Z)
2025-05-30T15:18:08.698Z [LOG] Checking for scheduled streams (2025-05-30T15:18:08.698Z to 2025-05-30T15:19:08.698Z)
2025-05-30T15:19:08.592Z [LOG] Checking for scheduled streams (2025-05-30T15:19:08.591Z to 2025-05-30T15:20:08.591Z)
2025-05-30T15:19:08.709Z [LOG] Checking for scheduled streams (2025-05-30T15:19:08.709Z to 2025-05-30T15:20:08.709Z)
2025-05-30T15:20:08.601Z [LOG] Checking for scheduled streams (2025-05-30T15:20:08.600Z to 2025-05-30T15:21:08.600Z)
2025-05-30T15:20:08.711Z [LOG] Checking for scheduled streams (2025-05-30T15:20:08.711Z to 2025-05-30T15:21:08.711Z)
2025-05-30T15:20:43.312Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T15:20:44.121Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T15:20:44.122Z [LOG] Stream scheduler initialized
2025-05-30T15:20:44.123Z [LOG] Checking for scheduled streams (2025-05-30T15:20:44.123Z to 2025-05-30T15:21:44.123Z)
2025-05-30T15:20:44.137Z [LOG] Checking database schema...
2025-05-30T15:20:44.201Z [LOG] StreamFlow running at:
2025-05-30T15:20:44.202Z [LOG]   http://**************:7575
2025-05-30T15:20:44.202Z [LOG]   http://************:7575
2025-05-30T15:20:44.203Z [LOG]   http://*************:7575
2025-05-30T15:20:44.204Z [LOG]   http://***********:7575
2025-05-30T15:20:44.205Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T15:20:44.267Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T15:20:44.268Z [LOG] Stream scheduler initialized
2025-05-30T15:20:44.269Z [LOG] Checking for scheduled streams (2025-05-30T15:20:44.268Z to 2025-05-30T15:21:44.268Z)
2025-05-30T15:20:44.271Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:20:44.273Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:20:45.228Z [LOG] ✅ Database migration completed
2025-05-30T15:20:45.232Z [LOG] Database migration completed successfully
2025-05-30T15:21:44.126Z [LOG] Checking for scheduled streams (2025-05-30T15:21:44.125Z to 2025-05-30T15:22:44.125Z)
2025-05-30T15:21:44.275Z [LOG] Checking for scheduled streams (2025-05-30T15:21:44.275Z to 2025-05-30T15:22:44.275Z)
2025-05-30T15:21:57.272Z [LOG] Checking plan name "Basic" - Found existing: none
2025-05-30T15:22:30.271Z [LOG] Editing plan 3fcadd4c-a17d-40bc-8b29-41b75ef15005 - Checking name "Basic" - Found existing: 3fcadd4c-a17d-40bc-8b29-41b75ef15005
2025-05-30T15:22:44.132Z [LOG] Checking for scheduled streams (2025-05-30T15:22:44.131Z to 2025-05-30T15:23:44.131Z)
2025-05-30T15:22:44.282Z [LOG] Checking for scheduled streams (2025-05-30T15:22:44.282Z to 2025-05-30T15:23:44.282Z)
2025-05-30T15:23:07.047Z [ERROR] Error creating stream: SQLITE_ERROR: table streams has no column named duration
2025-05-30T15:23:07.051Z [ERROR] Error creating stream: [Error: SQLITE_ERROR: table streams has no column named duration] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-05-30T15:29:20.638Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T15:29:21.504Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T15:29:21.508Z [LOG] Stream scheduler initialized
2025-05-30T15:29:21.509Z [LOG] Checking for scheduled streams (2025-05-30T15:29:21.509Z to 2025-05-30T15:30:21.509Z)
2025-05-30T15:29:21.523Z [LOG] Checking database schema...
2025-05-30T15:29:21.587Z [LOG] StreamFlow running at:
2025-05-30T15:29:21.588Z [LOG]   http://**************:7575
2025-05-30T15:29:21.590Z [LOG]   http://************:7575
2025-05-30T15:29:21.591Z [LOG]   http://*************:7575
2025-05-30T15:29:21.592Z [LOG]   http://***********:7575
2025-05-30T15:29:21.593Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T15:29:21.662Z [LOG] [StreamingService] No active streams found to restore
2025-05-30T15:29:21.663Z [LOG] Stream scheduler initialized
2025-05-30T15:29:21.664Z [LOG] Checking for scheduled streams (2025-05-30T15:29:21.663Z to 2025-05-30T15:30:21.663Z)
2025-05-30T15:29:21.665Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:29:21.667Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:29:22.611Z [LOG] ✅ Database migration completed
2025-05-30T15:29:22.612Z [LOG] Database migration completed successfully
2025-05-30T15:30:21.512Z [LOG] Checking for scheduled streams (2025-05-30T15:30:21.511Z to 2025-05-30T15:31:21.511Z)
2025-05-30T15:30:21.665Z [LOG] Checking for scheduled streams (2025-05-30T15:30:21.664Z to 2025-05-30T15:31:21.664Z)
2025-05-30T15:31:21.523Z [LOG] Checking for scheduled streams (2025-05-30T15:31:21.522Z to 2025-05-30T15:32:21.522Z)
2025-05-30T15:31:21.672Z [LOG] Checking for scheduled streams (2025-05-30T15:31:21.671Z to 2025-05-30T15:32:21.671Z)
2025-05-30T15:32:21.529Z [LOG] Checking for scheduled streams (2025-05-30T15:32:21.528Z to 2025-05-30T15:33:21.528Z)
2025-05-30T15:32:21.683Z [LOG] Checking for scheduled streams (2025-05-30T15:32:21.682Z to 2025-05-30T15:33:21.682Z)
2025-05-30T15:33:21.536Z [LOG] Checking for scheduled streams (2025-05-30T15:33:21.535Z to 2025-05-30T15:34:21.535Z)
2025-05-30T15:33:21.695Z [LOG] Checking for scheduled streams (2025-05-30T15:33:21.695Z to 2025-05-30T15:34:21.695Z)
2025-05-30T15:34:09.969Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T15:34:10.799Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T15:34:10.802Z [LOG] Stream scheduler initialized
2025-05-30T15:34:10.803Z [LOG] Checking for scheduled streams (2025-05-30T15:34:10.803Z to 2025-05-30T15:35:10.803Z)
2025-05-30T15:34:10.822Z [LOG] Checking database schema...
2025-05-30T15:34:10.873Z [LOG] StreamFlow running at:
2025-05-30T15:34:10.874Z [LOG]   http://**************:7575
2025-05-30T15:34:10.875Z [LOG]   http://************:7575
2025-05-30T15:34:10.876Z [LOG]   http://*************:7575
2025-05-30T15:34:10.878Z [LOG]   http://***********:7575
2025-05-30T15:34:10.879Z [LOG] [StreamingService] Checking for active streams to restore...
2025-05-30T15:34:10.947Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T15:34:10.948Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 has been running for 29143654 minutes, attempting to restore tracking...
2025-05-30T15:34:10.952Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:10.954Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:10.978Z [LOG] [StreamingService] Successfully restored tracking for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:10.979Z [LOG] Stream scheduler initialized
2025-05-30T15:34:10.980Z [LOG] Checking for scheduled streams (2025-05-30T15:34:10.980Z to 2025-05-30T15:35:10.980Z)
2025-05-30T15:34:10.983Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:34:10.988Z [LOG] [StreamingService] Stream status sync completed. Active streams: 1
2025-05-30T15:34:11.960Z [LOG] ✅ Database migration completed
2025-05-30T15:34:11.962Z [LOG] Database migration completed successfully
2025-05-30T15:34:12.015Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:12.027Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:12.029Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:12.030Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:15.043Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:15.044Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:19.042Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:19.048Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:19.049Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:19.050Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:22.059Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:22.060Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:22.957Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:22.963Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:22.964Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:22.964Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:25.982Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:25.983Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:26.254Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:26.260Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:26.261Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:26.262Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:28.295Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:34:28.758Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:34:29.275Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:29.276Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:30.165Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:30.171Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:30.172Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:30.174Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:33.191Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:33.192Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:34.472Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:34.479Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:34.480Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:34.481Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:37.491Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:37.493Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:37.743Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:37.748Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:37.749Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:37.750Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:38.776Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:34:40.758Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:40.760Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:41.028Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:41.034Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:41.035Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:41.036Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:44.041Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:44.043Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:44.305Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:44.310Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:44.311Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:44.312Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:45.368Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:34:45.370Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:34:45.378Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:46.893Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:34:47.325Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:47.326Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:48.028Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:34:50.622Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:50.627Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:50.628Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:50.629Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:50.999Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:34:51.001Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:34:51.015Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:52.127Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:34:52.667Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:34:53.640Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:53.641Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:53.910Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:53.916Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:53.917Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:53.918Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:56.824Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:34:56.926Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:56.928Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:34:57.226Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:34:57.232Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:34:57.233Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:57.234Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:34:57.307Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:34:59.109Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:34:59.112Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:34:59.120Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:00.072Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:35:00.238Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:00.240Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:35:00.544Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:35:01.133Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:35:01.140Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:35:01.141Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:01.142Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:01.902Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:35:01.904Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:35:01.913Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:02.749Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:35:03.178Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:35:04.149Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:04.151Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:35:04.407Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:35:04.413Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:35:04.414Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:04.415Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:07.425Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:07.426Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:35:07.756Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:35:07.764Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:35:07.766Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:07.767Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:10.766Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:10.767Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:35:10.804Z [LOG] Checking for scheduled streams (2025-05-30T15:35:10.804Z to 2025-05-30T15:36:10.804Z)
2025-05-30T15:35:10.979Z [LOG] Checking for scheduled streams (2025-05-30T15:35:10.979Z to 2025-05-30T15:36:10.979Z)
2025-05-30T15:35:11.643Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:35:11.649Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:35:11.650Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:35:11.651Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:40:35.959Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T15:40:36.739Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T15:40:36.741Z [LOG] Stream scheduler initialized
2025-05-30T15:40:36.743Z [LOG] Checking for scheduled streams (2025-05-30T15:40:36.742Z to 2025-05-30T15:41:36.742Z)
2025-05-30T15:40:36.756Z [LOG] Checking database schema...
2025-05-30T15:40:36.824Z [LOG] StreamFlow running at:
2025-05-30T15:40:36.826Z [LOG]   http://**************:7575
2025-05-30T15:40:36.827Z [LOG]   http://************:7575
2025-05-30T15:40:36.828Z [LOG]   http://*************:7575
2025-05-30T15:40:36.830Z [LOG]   http://***********:7575
2025-05-30T15:40:36.831Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T15:40:36.899Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T15:40:36.901Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T15:40:36.902Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T15:40:36.905Z [LOG] Stream scheduler initialized
2025-05-30T15:40:36.907Z [LOG] Checking for scheduled streams (2025-05-30T15:40:36.906Z to 2025-05-30T15:41:36.906Z)
2025-05-30T15:40:36.908Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:40:36.911Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T15:40:36.912Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T15:40:36.913Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:40:37.857Z [LOG] ✅ Database migration completed
2025-05-30T15:40:37.859Z [LOG] Database migration completed successfully
2025-05-30T15:40:49.521Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:40:50.080Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:00.073Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:03.755Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:41:03.757Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:41:03.767Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:05.169Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:06.352Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:16.368Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:20.364Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:20.366Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:41:20.704Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:41:20.716Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:41:20.718Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:20.719Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:22.167Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:22.659Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:23.730Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:23.732Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:41:24.001Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:41:24.008Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:41:24.010Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:24.011Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:27.022Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:27.025Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:41:31.012Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:41:31.021Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:41:31.022Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:31.023Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:32.664Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:34.039Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:34.043Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:41:34.214Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: true
2025-05-30T15:41:34.215Z [LOG] [StreamingService] Stopping active stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:34.221Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=null, Signal=SIGTERM
2025-05-30T15:41:34.222Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 was manually stopped, not restarting
2025-05-30T15:41:34.225Z [LOG] [StreamingService] Not saving history for stream beef630f-5881-48eb-8a1e-4ccacbf38069 - duration too short (0s)
2025-05-30T15:41:34.227Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:41:35.968Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:36.523Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:36.742Z [LOG] Checking for scheduled streams (2025-05-30T15:41:36.742Z to 2025-05-30T15:42:36.742Z)
2025-05-30T15:41:36.912Z [LOG] Checking for scheduled streams (2025-05-30T15:41:36.912Z to 2025-05-30T15:42:36.912Z)
2025-05-30T15:41:48.317Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T15:41:49.056Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T15:41:49.059Z [LOG] Stream scheduler initialized
2025-05-30T15:41:49.060Z [LOG] Checking for scheduled streams (2025-05-30T15:41:49.060Z to 2025-05-30T15:42:49.060Z)
2025-05-30T15:41:49.073Z [LOG] Checking database schema...
2025-05-30T15:41:49.131Z [LOG] StreamFlow running at:
2025-05-30T15:41:49.132Z [LOG]   http://**************:7575
2025-05-30T15:41:49.133Z [LOG]   http://************:7575
2025-05-30T15:41:49.134Z [LOG]   http://*************:7575
2025-05-30T15:41:49.135Z [LOG]   http://***********:7575
2025-05-30T15:41:49.136Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T15:41:49.205Z [LOG] [StreamingService] Found 1 streams marked as 'live' in database
2025-05-30T15:41:49.206Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T15:41:49.207Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T15:41:49.240Z [LOG] Stream scheduler initialized
2025-05-30T15:41:49.242Z [LOG] Checking for scheduled streams (2025-05-30T15:41:49.241Z to 2025-05-30T15:42:49.241Z)
2025-05-30T15:41:49.244Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:41:49.251Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T15:41:49.255Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T15:41:49.257Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:41:50.224Z [LOG] ✅ Database migration completed
2025-05-30T15:41:50.226Z [LOG] Database migration completed successfully
2025-05-30T15:41:59.057Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:41:59.620Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:42:09.667Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:42:19.629Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:42:29.618Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:42:39.622Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:42:49.073Z [LOG] Checking for scheduled streams (2025-05-30T15:42:49.073Z to 2025-05-30T15:43:49.073Z)
2025-05-30T15:42:49.243Z [LOG] Checking for scheduled streams (2025-05-30T15:42:49.243Z to 2025-05-30T15:43:49.243Z)
2025-05-30T15:42:49.636Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:42:59.631Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:43:01.588Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 1/3 slots used
2025-05-30T15:43:08.912Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:43:10.179Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:43:12.059Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:43:19.655Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:20.175Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:25.190Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:26.107Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:27.286Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:28.021Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:28.876Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:30.139Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:30.291Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:40.206Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:48.056Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:48.598Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:43:49.075Z [LOG] Checking for scheduled streams (2025-05-30T15:43:49.074Z to 2025-05-30T15:44:49.074Z)
2025-05-30T15:43:49.257Z [LOG] Checking for scheduled streams (2025-05-30T15:43:49.257Z to 2025-05-30T15:44:49.257Z)
2025-05-30T15:43:58.617Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:44:02.691Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:44:03.246Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:44:06.687Z [LOG] Scheduled stream 4ee53ec4-87b0-4612-8bbd-5a478165e71f was cancelled
2025-05-30T15:44:08.131Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:44:08.614Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:44:18.624Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:44:28.614Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:44:38.619Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:44:48.621Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:44:49.090Z [LOG] Checking for scheduled streams (2025-05-30T15:44:49.088Z to 2025-05-30T15:45:49.088Z)
2025-05-30T15:44:49.267Z [LOG] Checking for scheduled streams (2025-05-30T15:44:49.266Z to 2025-05-30T15:45:49.266Z)
2025-05-30T15:44:58.631Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:45:08.617Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:45:18.626Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:45:28.629Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:45:39.608Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:45:39.657Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:45:40.186Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:45:49.094Z [LOG] Checking for scheduled streams (2025-05-30T15:45:49.093Z to 2025-05-30T15:46:49.093Z)
2025-05-30T15:45:49.271Z [LOG] Checking for scheduled streams (2025-05-30T15:45:49.271Z to 2025-05-30T15:46:49.271Z)
2025-05-30T15:45:50.193Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:45:52.655Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:45:52.659Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:45:53.154Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:45:53.159Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:45:53.161Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:45:53.162Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:45:54.595Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:45:55.089Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:45:56.178Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:45:56.179Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:45:56.468Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:45:56.475Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:45:56.476Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:45:56.478Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:45:56.673Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:45:56.676Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:45:56.686Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:45:57.863Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:45:58.398Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:45:59.493Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:45:59.496Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:03.493Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:46:03.503Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:46:03.504Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:03.505Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:06.517Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:06.519Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:06.801Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:46:06.807Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:46:06.809Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:06.810Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:08.352Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:09.839Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:09.841Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:10.432Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:10.817Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:46:10.822Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:46:10.824Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:10.825Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:11.055Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:12.281Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:46:12.284Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:46:12.291Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:13.505Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:13.833Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:13.834Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:14.123Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:16.036Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:46:16.042Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:46:16.043Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:16.045Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:17.158Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:46:17.160Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:46:17.169Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:18.275Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:18.846Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:19.061Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:19.063Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:19.972Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:46:19.980Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:46:19.982Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:19.984Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:22.989Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:22.991Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:23.287Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:46:23.293Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:46:23.294Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:23.296Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:23.385Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:23.946Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:26.301Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:26.303Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:26.664Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:46:26.671Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:46:26.672Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:26.674Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:29.681Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:29.683Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:30.985Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:46:30.993Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:46:30.995Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:30.997Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:33.947Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:34.011Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:34.013Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:34.930Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:46:34.938Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:46:34.939Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:34.941Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:36.855Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:46:36.857Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:46:36.866Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:37.948Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:37.950Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:46:38.029Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:38.522Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:40.165Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: true
2025-05-30T15:46:40.167Z [LOG] [StreamingService] Stopping active stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:40.174Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=null, Signal=SIGTERM
2025-05-30T15:46:40.176Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 was manually stopped, not restarting
2025-05-30T15:46:40.180Z [LOG] [StreamingService] Not saving history for stream beef630f-5881-48eb-8a1e-4ccacbf38069 - duration too short (2s)
2025-05-30T15:46:40.182Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:46:41.011Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:41.469Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:43.582Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:44.134Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:46:49.071Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:46:49.075Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T15:46:49.078Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T15:46:49.081Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T15:46:49.085Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T15:46:49.086Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:46:49.102Z [LOG] Checking for scheduled streams (2025-05-30T15:46:49.102Z to 2025-05-30T15:47:49.102Z)
2025-05-30T15:46:49.274Z [LOG] Checking for scheduled streams (2025-05-30T15:46:49.274Z to 2025-05-30T15:47:49.274Z)
2025-05-30T15:46:54.144Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:47:04.139Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:47:14.151Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:47:24.139Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:47:34.149Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:47:44.128Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:47:49.112Z [LOG] Checking for scheduled streams (2025-05-30T15:47:49.112Z to 2025-05-30T15:48:49.112Z)
2025-05-30T15:47:49.282Z [LOG] Checking for scheduled streams (2025-05-30T15:47:49.282Z to 2025-05-30T15:48:49.282Z)
2025-05-30T15:47:54.141Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:04.145Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:14.140Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:20.329Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:20.903Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:23.354Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:23.847Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:33.858Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:40.573Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:41.142Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:48.543Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:48:48.546Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:48:48.845Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:48:48.852Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:48:48.854Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:48:48.856Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:48:49.123Z [LOG] Checking for scheduled streams (2025-05-30T15:48:49.123Z to 2025-05-30T15:49:49.123Z)
2025-05-30T15:48:49.289Z [LOG] Checking for scheduled streams (2025-05-30T15:48:49.288Z to 2025-05-30T15:49:49.288Z)
2025-05-30T15:48:50.279Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:50.753Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:51.866Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:48:51.867Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:48:52.140Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:48:52.146Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:48:52.148Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:48:52.149Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:48:55.168Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:48:55.170Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:48:55.720Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: true
2025-05-30T15:48:55.721Z [LOG] [StreamingService] Stopping active stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:48:55.735Z [LOG] [StreamingService] Not saving history for stream beef630f-5881-48eb-8a1e-4ccacbf38069 - duration too short (0s)
2025-05-30T15:48:55.737Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=null, Signal=SIGTERM
2025-05-30T15:48:55.738Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 was manually stopped, not restarting
2025-05-30T15:48:55.741Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:48:57.286Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:48:57.773Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:49:03.097Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:49:07.782Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:49:17.789Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:49:27.780Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:49:37.771Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 2/3 slots used
2025-05-30T15:49:42.183Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:49:42.709Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:49:49.132Z [LOG] Checking for scheduled streams (2025-05-30T15:49:49.131Z to 2025-05-30T15:50:49.131Z)
2025-05-30T15:49:49.301Z [LOG] Checking for scheduled streams (2025-05-30T15:49:49.300Z to 2025-05-30T15:50:49.300Z)
2025-05-30T15:49:52.725Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:49:57.414Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:49:57.982Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:50:08.007Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:50:13.799Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:13.801Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:50:14.812Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:50:14.820Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:50:14.822Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:14.823Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:15.265Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:50:15.730Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:50:17.838Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:17.840Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:50:19.813Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:50:19.824Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:50:19.825Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:19.826Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:22.843Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:22.846Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:50:24.842Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:50:24.853Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:50:24.854Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:24.856Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:25.734Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:50:27.872Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:27.875Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:50:28.456Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: true
2025-05-30T15:50:28.458Z [LOG] [StreamingService] Stopping active stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:28.470Z [LOG] [StreamingService] Not saving history for stream beef630f-5881-48eb-8a1e-4ccacbf38069 - duration too short (0s)
2025-05-30T15:50:28.473Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=null, Signal=SIGTERM
2025-05-30T15:50:28.474Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 was manually stopped, not restarting
2025-05-30T15:50:28.478Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:50:30.182Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:50:30.736Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:50:40.734Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:50:49.133Z [LOG] Checking for scheduled streams (2025-05-30T15:50:49.132Z to 2025-05-30T15:51:49.132Z)
2025-05-30T15:50:49.316Z [LOG] Checking for scheduled streams (2025-05-30T15:50:49.315Z to 2025-05-30T15:51:49.315Z)
2025-05-30T15:50:50.736Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:51:00.743Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:51:10.752Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:51:20.751Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:51:30.750Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:51:40.750Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:51:49.086Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:51:49.089Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T15:51:49.094Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T15:51:49.096Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T15:51:49.098Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T15:51:49.100Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T15:51:49.103Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T15:51:49.105Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:51:49.141Z [LOG] Checking for scheduled streams (2025-05-30T15:51:49.140Z to 2025-05-30T15:52:49.140Z)
2025-05-30T15:51:49.320Z [LOG] Checking for scheduled streams (2025-05-30T15:51:49.320Z to 2025-05-30T15:52:49.320Z)
2025-05-30T15:51:50.750Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:52:00.752Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:52:10.746Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:52:20.773Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:52:30.734Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:52:40.732Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:52:49.147Z [LOG] Checking for scheduled streams (2025-05-30T15:52:49.147Z to 2025-05-30T15:53:49.147Z)
2025-05-30T15:52:49.332Z [LOG] Checking for scheduled streams (2025-05-30T15:52:49.332Z to 2025-05-30T15:53:49.332Z)
2025-05-30T15:52:50.729Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:53:00.733Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:53:10.746Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:53:23.799Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:53:24.301Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:53:34.309Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:53:44.316Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:53:49.152Z [LOG] Checking for scheduled streams (2025-05-30T15:53:49.151Z to 2025-05-30T15:54:49.151Z)
2025-05-30T15:53:49.338Z [LOG] Checking for scheduled streams (2025-05-30T15:53:49.337Z to 2025-05-30T15:54:49.337Z)
2025-05-30T15:53:54.304Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:54:04.316Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:54:14.322Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:54:24.315Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:54:34.318Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:54:44.311Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:54:49.154Z [LOG] Checking for scheduled streams (2025-05-30T15:54:49.153Z to 2025-05-30T15:55:49.153Z)
2025-05-30T15:54:49.345Z [LOG] Checking for scheduled streams (2025-05-30T15:54:49.344Z to 2025-05-30T15:55:49.344Z)
2025-05-30T15:54:54.315Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:54:58.874Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:54:59.701Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:55:09.684Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:55:19.690Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:55:20.070Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:20.071Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:20.357Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:20.363Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:20.364Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:20.365Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:21.769Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:55:22.231Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:55:23.371Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:23.373Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:24.270Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:24.276Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:24.277Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:24.277Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:27.284Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:27.286Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:27.553Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:27.559Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:27.560Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:27.561Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:30.565Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:30.566Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:30.826Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:30.832Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:30.833Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:30.835Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:32.258Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:55:33.850Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:33.852Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:34.106Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:34.110Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:34.111Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:34.112Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:37.118Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:37.120Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:38.392Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:38.398Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:38.399Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:38.401Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:41.419Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:41.421Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:42.247Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:55:42.303Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:42.309Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:42.311Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:42.311Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:45.318Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:45.320Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:47.227Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:47.233Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:47.235Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:47.236Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:49.164Z [LOG] Checking for scheduled streams (2025-05-30T15:55:49.163Z to 2025-05-30T15:56:49.163Z)
2025-05-30T15:55:49.349Z [LOG] Checking for scheduled streams (2025-05-30T15:55:49.348Z to 2025-05-30T15:56:49.348Z)
2025-05-30T15:55:50.242Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:50.244Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:51.160Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:51.166Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:51.168Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:51.168Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:52.230Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:55:54.182Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:54.184Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:55.080Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:55.087Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:55.088Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:55.089Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:58.099Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:58.100Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:55:58.357Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:55:58.364Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:55:58.365Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:55:58.366Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:00.189Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:56:00.191Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:56:00.196Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:01.383Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:01.384Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:01.966Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:02.410Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:02.675Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:02.680Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:02.681Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:02.682Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:03.931Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:56:03.933Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:56:03.938Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:04.921Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:05.355Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:05.698Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:05.699Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:05.953Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:05.959Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:05.961Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:05.962Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:08.978Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:08.980Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:12.270Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:12.275Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:12.276Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:12.278Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:15.281Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:15.282Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:15.365Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:16.176Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:16.184Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:16.185Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:16.186Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:17.303Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:56:17.305Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:56:17.312Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:18.294Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:18.857Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:19.192Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:19.193Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:20.067Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:20.072Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:20.073Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:20.074Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:20.565Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:20.973Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:22.243Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:56:22.245Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:56:22.252Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:23.081Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:23.083Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:23.159Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:23.359Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:23.366Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:23.368Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:23.369Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:23.641Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:25.250Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:56:25.252Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:56:25.258Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:26.083Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:26.378Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:26.380Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:26.549Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:26.660Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:26.668Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:26.669Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:26.670Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:28.505Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:56:28.506Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:56:28.515Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:29.086Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:29.580Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:29.679Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:29.683Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:29.965Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:29.970Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:29.972Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:29.973Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:32.978Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:32.979Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:33.255Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:33.261Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:33.262Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:33.263Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:36.280Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:36.282Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:56:36.541Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:56:36.546Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:56:36.547Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:36.548Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:56:44.611Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T15:56:45.279Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T15:56:45.281Z [LOG] Stream scheduler initialized
2025-05-30T15:56:45.282Z [LOG] Checking for scheduled streams (2025-05-30T15:56:45.282Z to 2025-05-30T15:57:45.282Z)
2025-05-30T15:56:45.294Z [LOG] Checking database schema...
2025-05-30T15:56:45.346Z [LOG] StreamFlow running at:
2025-05-30T15:56:45.347Z [LOG]   http://**************:7575
2025-05-30T15:56:45.348Z [LOG]   http://************:7575
2025-05-30T15:56:45.348Z [LOG]   http://*************:7575
2025-05-30T15:56:45.349Z [LOG]   http://***********:7575
2025-05-30T15:56:45.350Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T15:56:45.411Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T15:56:45.412Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T15:56:45.413Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T15:56:45.415Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T15:56:45.416Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T15:56:45.418Z [LOG] Stream scheduler initialized
2025-05-30T15:56:45.419Z [LOG] Checking for scheduled streams (2025-05-30T15:56:45.419Z to 2025-05-30T15:57:45.419Z)
2025-05-30T15:56:45.421Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T15:56:45.423Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T15:56:45.424Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T15:56:45.426Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T15:56:45.429Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T15:56:45.430Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T15:56:45.431Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T15:56:45.432Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T15:56:46.375Z [LOG] ✅ Database migration completed
2025-05-30T15:56:46.376Z [LOG] Database migration completed successfully
2025-05-30T15:56:53.876Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:54.348Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:56:59.888Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:56:59.890Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:56:59.897Z [LOG] Reset schedule_time for stopped stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:57:01.362Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:02.443Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:10.930Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:57:10.931Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:57:11.197Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:57:11.203Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:57:11.204Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:57:11.205Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:57:12.762Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:12.792Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:13.215Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:14.218Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:57:14.219Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:57:15.471Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:57:15.480Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:57:15.481Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:57:15.482Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:57:18.490Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:57:18.492Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/123
2025-05-30T15:57:18.774Z [ERROR] [FFMPEG_STDERR] beef630f-5881-48eb-8a1e-4ccacbf38069: rtmp://a.rtmp.youtube.com/live2/123: I/O error
2025-05-30T15:57:18.775Z [LOG] [StreamingService] Auto-stopping stream beef630f-5881-48eb-8a1e-4ccacbf38069: Too many I/O errors (3)
2025-05-30T15:57:18.777Z [LOG] [StreamingService] Critical error detected, auto-stopping stream beef630f-5881-48eb-8a1e-4ccacbf38069: Too many I/O errors (3/3)
2025-05-30T15:57:18.781Z [LOG] [FFMPEG_EXIT] beef630f-5881-48eb-8a1e-4ccacbf38069: Code=1, Signal=null
2025-05-30T15:57:18.783Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream beef630f-5881-48eb-8a1e-4ccacbf38069
2025-05-30T15:57:18.784Z [LOG] [StreamingService] Auto-stopping stream beef630f-5881-48eb-8a1e-4ccacbf38069: Too many I/O errors (3)
2025-05-30T15:57:18.785Z [LOG] [StreamingService] Auto-stopping stream beef630f-5881-48eb-8a1e-4ccacbf38069: Too many I/O errors (3/3)
2025-05-30T15:57:18.786Z [LOG] [StreamingService] Auto-stopping stream beef630f-5881-48eb-8a1e-4ccacbf38069: Too many I/O errors (3/3)
2025-05-30T15:57:18.787Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:57:18.789Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:57:19.788Z [LOG] [StreamingService] Auto-stopping stream beef630f-5881-48eb-8a1e-4ccacbf38069: Too many I/O errors (3/3)
2025-05-30T15:57:19.790Z [LOG] [StreamingService] Stop request for stream beef630f-5881-48eb-8a1e-4ccacbf38069, isActive: false
2025-05-30T15:57:23.215Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:33.216Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:43.214Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:45.279Z [LOG] Checking for scheduled streams (2025-05-30T15:57:45.278Z to 2025-05-30T15:58:45.278Z)
2025-05-30T15:57:45.430Z [LOG] Checking for scheduled streams (2025-05-30T15:57:45.429Z to 2025-05-30T15:58:45.429Z)
2025-05-30T15:57:53.221Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:56.210Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream f4f91b2e-1194-4c0d-bace-b187f1493521
2025-05-30T15:57:56.215Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/grsdg
2025-05-30T15:57:57.609Z [ERROR] [FFMPEG_STDERR] f4f91b2e-1194-4c0d-bace-b187f1493521: rtmp://a.rtmp.youtube.com/live2/grsdg: I/O error
2025-05-30T15:57:57.614Z [LOG] [FFMPEG_EXIT] f4f91b2e-1194-4c0d-bace-b187f1493521: Code=1, Signal=null
2025-05-30T15:57:57.616Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream f4f91b2e-1194-4c0d-bace-b187f1493521
2025-05-30T15:57:57.617Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream f4f91b2e-1194-4c0d-bace-b187f1493521
2025-05-30T15:57:57.997Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:57:58.540Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:58:00.631Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream f4f91b2e-1194-4c0d-bace-b187f1493521
2025-05-30T15:58:00.633Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/grsdg
2025-05-30T15:58:01.898Z [ERROR] [FFMPEG_STDERR] f4f91b2e-1194-4c0d-bace-b187f1493521: rtmp://a.rtmp.youtube.com/live2/grsdg: I/O error
2025-05-30T15:58:01.904Z [LOG] [FFMPEG_EXIT] f4f91b2e-1194-4c0d-bace-b187f1493521: Code=1, Signal=null
2025-05-30T15:58:01.906Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream f4f91b2e-1194-4c0d-bace-b187f1493521
2025-05-30T15:58:01.907Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream f4f91b2e-1194-4c0d-bace-b187f1493521
2025-05-30T15:58:04.914Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream f4f91b2e-1194-4c0d-bace-b187f1493521
2025-05-30T15:58:04.915Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize rtmp://a.rtmp.youtube.com/live2/grsdg
2025-05-30T15:58:08.222Z [ERROR] [FFMPEG_STDERR] f4f91b2e-1194-4c0d-bace-b187f1493521: rtmp://a.rtmp.youtube.com/live2/grsdg: I/O error
2025-05-30T15:58:08.223Z [LOG] [StreamingService] Auto-stopping stream f4f91b2e-1194-4c0d-bace-b187f1493521: Too many I/O errors (3)
2025-05-30T15:58:08.225Z [LOG] [StreamingService] Critical error detected, auto-stopping stream f4f91b2e-1194-4c0d-bace-b187f1493521: Too many I/O errors (3/3)
2025-05-30T15:58:08.228Z [LOG] [FFMPEG_EXIT] f4f91b2e-1194-4c0d-bace-b187f1493521: Code=1, Signal=null
2025-05-30T15:58:08.230Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream f4f91b2e-1194-4c0d-bace-b187f1493521
2025-05-30T15:58:08.231Z [LOG] [StreamingService] Auto-stopping stream f4f91b2e-1194-4c0d-bace-b187f1493521: Too many I/O errors (3)
2025-05-30T15:58:08.232Z [LOG] [StreamingService] Auto-stopping stream f4f91b2e-1194-4c0d-bace-b187f1493521: Too many I/O errors (3/3)
2025-05-30T15:58:08.233Z [LOG] [StreamingService] Auto-stopping stream f4f91b2e-1194-4c0d-bace-b187f1493521: Too many I/O errors (3/3)
2025-05-30T15:58:08.234Z [LOG] [StreamingService] Stop request for stream f4f91b2e-1194-4c0d-bace-b187f1493521, isActive: false
2025-05-30T15:58:08.236Z [LOG] [StreamingService] Stream f4f91b2e-1194-4c0d-bace-b187f1493521 not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T15:58:08.550Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:58:09.228Z [LOG] [StreamingService] Auto-stopping stream f4f91b2e-1194-4c0d-bace-b187f1493521: Too many I/O errors (3/3)
2025-05-30T15:58:09.230Z [LOG] [StreamingService] Stop request for stream f4f91b2e-1194-4c0d-bace-b187f1493521, isActive: false
2025-05-30T15:58:18.549Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:58:28.537Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:58:38.534Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:58:45.288Z [LOG] Checking for scheduled streams (2025-05-30T15:58:45.288Z to 2025-05-30T15:59:45.288Z)
2025-05-30T15:58:45.439Z [LOG] Checking for scheduled streams (2025-05-30T15:58:45.438Z to 2025-05-30T15:59:45.438Z)
2025-05-30T15:58:48.550Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:58:58.556Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:59:08.543Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:59:18.552Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:59:28.552Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:59:38.551Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:59:45.291Z [LOG] Checking for scheduled streams (2025-05-30T15:59:45.290Z to 2025-05-30T16:00:45.290Z)
2025-05-30T15:59:45.441Z [LOG] Checking for scheduled streams (2025-05-30T15:59:45.440Z to 2025-05-30T16:00:45.440Z)
2025-05-30T15:59:48.548Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T15:59:58.552Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:00:08.553Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:00:18.545Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:00:28.549Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:00:38.557Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:00:45.304Z [LOG] Checking for scheduled streams (2025-05-30T16:00:45.304Z to 2025-05-30T16:01:45.304Z)
2025-05-30T16:00:45.449Z [LOG] Checking for scheduled streams (2025-05-30T16:00:45.449Z to 2025-05-30T16:01:45.449Z)
2025-05-30T16:00:48.551Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:00:58.549Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:01:08.554Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:01:18.542Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:01:28.542Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:01:38.589Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:01:45.285Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T16:01:45.287Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T16:01:45.288Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T16:01:45.289Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T16:01:45.290Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T16:01:45.292Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T16:01:45.294Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T16:01:45.295Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T16:01:45.310Z [LOG] Checking for scheduled streams (2025-05-30T16:01:45.309Z to 2025-05-30T16:02:45.309Z)
2025-05-30T16:01:45.459Z [LOG] Checking for scheduled streams (2025-05-30T16:01:45.458Z to 2025-05-30T16:02:45.458Z)
2025-05-30T16:01:48.553Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:01:58.551Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:02:08.553Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:02:18.553Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:02:28.541Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:02:38.547Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:02:45.318Z [LOG] Checking for scheduled streams (2025-05-30T16:02:45.317Z to 2025-05-30T16:03:45.317Z)
2025-05-30T16:02:45.469Z [LOG] Checking for scheduled streams (2025-05-30T16:02:45.468Z to 2025-05-30T16:03:45.468Z)
2025-05-30T16:02:48.547Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:02:58.554Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:03:08.546Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:03:18.552Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:03:28.548Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:03:38.543Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:03:45.322Z [LOG] Checking for scheduled streams (2025-05-30T16:03:45.322Z to 2025-05-30T16:04:45.322Z)
2025-05-30T16:03:45.480Z [LOG] Checking for scheduled streams (2025-05-30T16:03:45.479Z to 2025-05-30T16:04:45.479Z)
2025-05-30T16:03:48.542Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:03:58.540Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:04:08.540Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:04:19.310Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:04:29.311Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:04:39.309Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:04:45.324Z [LOG] Checking for scheduled streams (2025-05-30T16:04:45.323Z to 2025-05-30T16:05:45.323Z)
2025-05-30T16:04:45.485Z [LOG] Checking for scheduled streams (2025-05-30T16:04:45.483Z to 2025-05-30T16:05:45.483Z)
2025-05-30T16:04:49.315Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:04:56.655Z [LOG] User created successfully with ID: 8c644f1a-b592-4caa-9814-aced9878f28c
2025-05-30T16:04:56.666Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:04:57.438Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:04:59.304Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:05:04.747Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:05:09.318Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:05:09.950Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:05:10.322Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:05:20.326Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:05:30.328Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:05:40.327Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:05:43.955Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:05:45.331Z [LOG] Checking for scheduled streams (2025-05-30T16:05:45.330Z to 2025-05-30T16:06:45.330Z)
2025-05-30T16:05:45.485Z [LOG] Checking for scheduled streams (2025-05-30T16:05:45.484Z to 2025-05-30T16:06:45.484Z)
2025-05-30T16:06:18.616Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:06:28.542Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:06:30.415Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:06:30.933Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:06:41.589Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:06:44.748Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:06:45.169Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:06:45.293Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T16:06:45.295Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T16:06:45.297Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T16:06:45.298Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T16:06:45.300Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T16:06:45.302Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T16:06:45.304Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T16:06:45.305Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T16:06:45.345Z [LOG] Checking for scheduled streams (2025-05-30T16:06:45.344Z to 2025-05-30T16:07:45.344Z)
2025-05-30T16:06:45.492Z [LOG] Checking for scheduled streams (2025-05-30T16:06:45.491Z to 2025-05-30T16:07:45.491Z)
2025-05-30T16:06:56.449Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:07:04.517Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:07:04.995Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:07:14.999Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:07:21.478Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:21.479Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize 232/2323
2025-05-30T16:07:21.491Z [LOG] Scheduling termination for stream 0897b0b9-3de5-499a-814d-c370f9c6540d after 46 minutes
2025-05-30T16:07:21.565Z [ERROR] [FFMPEG_STDERR] 0897b0b9-3de5-499a-814d-c370f9c6540d: 232/2323: No such file or directory
2025-05-30T16:07:21.570Z [LOG] [FFMPEG_EXIT] 0897b0b9-3de5-499a-814d-c370f9c6540d: Code=1, Signal=null
2025-05-30T16:07:21.571Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:21.573Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:22.992Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:07:23.452Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:07:24.590Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:24.592Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize 232/2323
2025-05-30T16:07:24.604Z [LOG] Scheduling termination for stream 0897b0b9-3de5-499a-814d-c370f9c6540d after 46 minutes
2025-05-30T16:07:24.676Z [ERROR] [FFMPEG_STDERR] 0897b0b9-3de5-499a-814d-c370f9c6540d: 232/2323: No such file or directory
2025-05-30T16:07:24.681Z [LOG] [FFMPEG_EXIT] 0897b0b9-3de5-499a-814d-c370f9c6540d: Code=1, Signal=null
2025-05-30T16:07:24.681Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:24.682Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:27.692Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:27.694Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize 232/2323
2025-05-30T16:07:27.706Z [LOG] Scheduling termination for stream 0897b0b9-3de5-499a-814d-c370f9c6540d after 46 minutes
2025-05-30T16:07:27.775Z [ERROR] [FFMPEG_STDERR] 0897b0b9-3de5-499a-814d-c370f9c6540d: 232/2323: No such file or directory
2025-05-30T16:07:27.779Z [LOG] [FFMPEG_EXIT] 0897b0b9-3de5-499a-814d-c370f9c6540d: Code=1, Signal=null
2025-05-30T16:07:27.780Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:27.781Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:30.789Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:30.791Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize 232/2323
2025-05-30T16:07:30.801Z [LOG] Scheduling termination for stream 0897b0b9-3de5-499a-814d-c370f9c6540d after 46 minutes
2025-05-30T16:07:30.872Z [ERROR] [FFMPEG_STDERR] 0897b0b9-3de5-499a-814d-c370f9c6540d: 232/2323: No such file or directory
2025-05-30T16:07:30.879Z [LOG] [FFMPEG_EXIT] 0897b0b9-3de5-499a-814d-c370f9c6540d: Code=1, Signal=null
2025-05-30T16:07:30.880Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:30.881Z [LOG] [StreamingService] FFmpeg exited with code 1. Attempting restart #1 for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:33.473Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:07:33.889Z [LOG] [StreamingService] HEVC video detected, using re-encoding mode for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:33.891Z [LOG] Starting stream: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe -hwaccel none -loglevel error -re -fflags +genpts+discardcorrupt -avoid_negative_ts make_zero -stream_loop -1 -i C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\video-1748611878594-674066304.mp4 -c:v libx264 -preset ultrafast -tune zerolatency -b:v 2500k -maxrate 3000k -bufsize 3750k -pix_fmt yuv420p -g 60 -keyint_min 60 -sc_threshold 0 -s 1280x720 -r 30 -c:a aac -b:a 128k -ar 44100 -ac 2 -f flv -flvflags no_duration_filesize 232/2323
2025-05-30T16:07:33.903Z [LOG] Scheduling termination for stream 0897b0b9-3de5-499a-814d-c370f9c6540d after 46 minutes
2025-05-30T16:07:33.988Z [ERROR] [FFMPEG_STDERR] 0897b0b9-3de5-499a-814d-c370f9c6540d: 232/2323: No such file or directory
2025-05-30T16:07:33.994Z [LOG] [FFMPEG_EXIT] 0897b0b9-3de5-499a-814d-c370f9c6540d: Code=1, Signal=null
2025-05-30T16:07:33.995Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:33.996Z [LOG] [StreamingService] Auto-stopping stream 0897b0b9-3de5-499a-814d-c370f9c6540d: Too many consecutive failures (5)
2025-05-30T16:07:33.997Z [LOG] [StreamingService] Auto-stopping stream 0897b0b9-3de5-499a-814d-c370f9c6540d: Too many consecutive failures (5/5)
2025-05-30T16:07:33.998Z [LOG] [StreamingService] Auto-stopping stream 0897b0b9-3de5-499a-814d-c370f9c6540d: Too many consecutive failures (5/5)
2025-05-30T16:07:33.999Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T16:07:34.002Z [LOG] [StreamingService] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d not active in memory but status is 'live' in DB. Fixing status.
2025-05-30T16:07:34.009Z [LOG] Cancelled scheduled termination for stream 0897b0b9-3de5-499a-814d-c370f9c6540d
2025-05-30T16:07:43.461Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:07:45.356Z [LOG] Checking for scheduled streams (2025-05-30T16:07:45.355Z to 2025-05-30T16:08:45.355Z)
2025-05-30T16:07:45.358Z [LOG] Scheduling termination for stream 0897b0b9-3de5-499a-814d-c370f9c6540d after 45.80896666666667 minutes
2025-05-30T16:07:45.503Z [LOG] Checking for scheduled streams (2025-05-30T16:07:45.502Z to 2025-05-30T16:08:45.502Z)
2025-05-30T16:07:53.464Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:08:07.841Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:08:14.644Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:08:23.457Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:08:33.456Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:08:43.454Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:08:45.361Z [LOG] Checking for scheduled streams (2025-05-30T16:08:45.361Z to 2025-05-30T16:09:45.361Z)
2025-05-30T16:08:45.509Z [LOG] Checking for scheduled streams (2025-05-30T16:08:45.509Z to 2025-05-30T16:09:45.509Z)
2025-05-30T16:08:53.454Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:09:07.169Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:09:13.458Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:09:16.931Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:09:17.418Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:09:27.420Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:09:45.369Z [LOG] Checking for scheduled streams (2025-05-30T16:09:45.368Z to 2025-05-30T16:10:45.368Z)
2025-05-30T16:09:45.520Z [LOG] Checking for scheduled streams (2025-05-30T16:09:45.520Z to 2025-05-30T16:10:45.520Z)
2025-05-30T16:10:45.370Z [LOG] Checking for scheduled streams (2025-05-30T16:10:45.369Z to 2025-05-30T16:11:45.369Z)
2025-05-30T16:10:45.533Z [LOG] Checking for scheduled streams (2025-05-30T16:10:45.533Z to 2025-05-30T16:11:45.533Z)
2025-05-30T16:11:01.593Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:11:07.422Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:11:17.428Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:11:27.426Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:11:37.426Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:11:45.296Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T16:11:45.299Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T16:11:45.302Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T16:11:45.304Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T16:11:45.306Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T16:11:45.307Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T16:11:45.311Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T16:11:45.313Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T16:11:45.378Z [LOG] Checking for scheduled streams (2025-05-30T16:11:45.376Z to 2025-05-30T16:12:45.376Z)
2025-05-30T16:11:45.541Z [LOG] Checking for scheduled streams (2025-05-30T16:11:45.540Z to 2025-05-30T16:12:45.540Z)
2025-05-30T16:11:47.423Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:11:57.424Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:12:07.431Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:12:17.425Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:12:18.805Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 removed from failed streams list after cooldown
2025-05-30T16:12:19.804Z [LOG] [StreamingService] Stream beef630f-5881-48eb-8a1e-4ccacbf38069 removed from failed streams list after cooldown
2025-05-30T16:12:27.424Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:12:37.423Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:12:45.378Z [LOG] Checking for scheduled streams (2025-05-30T16:12:45.377Z to 2025-05-30T16:13:45.377Z)
2025-05-30T16:12:45.553Z [LOG] Checking for scheduled streams (2025-05-30T16:12:45.553Z to 2025-05-30T16:13:45.553Z)
2025-05-30T16:12:47.445Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:12:58.314Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:13:08.247Z [LOG] [StreamingService] Stream f4f91b2e-1194-4c0d-bace-b187f1493521 removed from failed streams list after cooldown
2025-05-30T16:13:08.326Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:13:09.250Z [LOG] [StreamingService] Stream f4f91b2e-1194-4c0d-bace-b187f1493521 removed from failed streams list after cooldown
2025-05-30T16:13:18.335Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:13:28.315Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:13:38.322Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:13:45.380Z [LOG] Checking for scheduled streams (2025-05-30T16:13:45.379Z to 2025-05-30T16:14:45.379Z)
2025-05-30T16:13:45.568Z [LOG] Checking for scheduled streams (2025-05-30T16:13:45.568Z to 2025-05-30T16:14:45.568Z)
2025-05-30T16:14:13.038Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:14:13.747Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:14:14.434Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:14:25.307Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:14:35.321Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:14:45.319Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:14:45.390Z [LOG] Checking for scheduled streams (2025-05-30T16:14:45.390Z to 2025-05-30T16:15:45.390Z)
2025-05-30T16:14:45.571Z [LOG] Checking for scheduled streams (2025-05-30T16:14:45.570Z to 2025-05-30T16:15:45.570Z)
2025-05-30T16:14:55.316Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:15:05.316Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:15:13.342Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:15:13.892Z [LOG] [Quota Check] User 8c644f1a-b592-4caa-9814-aced9878f28c: 0/1 slots used
2025-05-30T16:15:15.302Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:15:45.392Z [LOG] Checking for scheduled streams (2025-05-30T16:15:45.392Z to 2025-05-30T16:16:45.392Z)
2025-05-30T16:15:45.583Z [LOG] Checking for scheduled streams (2025-05-30T16:15:45.582Z to 2025-05-30T16:16:45.582Z)
2025-05-30T16:15:46.897Z [LOG] User created successfully with ID: fb3211c5-297d-4098-a6a3-05b700c28635
2025-05-30T16:15:46.912Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:15:47.424Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:15:58.321Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:16:07.422Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:16:09.084Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:16:12.658Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:16:13.140Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:16:18.646Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:16:23.142Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:16:24.717Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:16:33.374Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:16:34.458Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:16:43.308Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:16:45.328Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T16:16:45.360Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T16:16:45.369Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T16:16:45.371Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T16:16:45.376Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T16:16:45.380Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T16:16:45.384Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T16:16:45.385Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T16:16:45.401Z [LOG] Checking for scheduled streams (2025-05-30T16:16:45.392Z to 2025-05-30T16:17:45.392Z)
2025-05-30T16:16:45.408Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:16:49.481Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T16:16:50.315Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T16:16:50.318Z [LOG] Stream scheduler initialized
2025-05-30T16:16:50.319Z [LOG] Checking for scheduled streams (2025-05-30T16:16:50.319Z to 2025-05-30T16:17:50.319Z)
2025-05-30T16:16:50.393Z [LOG] Checking database schema...
2025-05-30T16:16:50.508Z [LOG] StreamFlow running at:
2025-05-30T16:16:50.509Z [LOG]   http://**************:7575
2025-05-30T16:16:50.510Z [LOG]   http://************:7575
2025-05-30T16:16:50.511Z [LOG]   http://*************:7575
2025-05-30T16:16:50.512Z [LOG]   http://***********:7575
2025-05-30T16:16:50.513Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T16:16:50.580Z [LOG] Scheduling termination for stream 0897b0b9-3de5-499a-814d-c370f9c6540d after 36.72193333333333 minutes
2025-05-30T16:16:50.582Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T16:16:50.582Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T16:16:50.583Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T16:16:50.585Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T16:16:50.587Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T16:16:50.589Z [LOG] Stream scheduler initialized
2025-05-30T16:16:50.590Z [LOG] Checking for scheduled streams (2025-05-30T16:16:50.590Z to 2025-05-30T16:17:50.590Z)
2025-05-30T16:16:50.593Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T16:16:50.595Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T16:16:50.598Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T16:16:50.599Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T16:16:50.601Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T16:16:50.602Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T16:16:50.604Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T16:16:50.604Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T16:16:51.528Z [LOG] ✅ Database migration completed
2025-05-30T16:16:51.529Z [LOG] Database migration completed successfully
2025-05-30T16:17:11.293Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:17:11.898Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:17:50.321Z [LOG] Checking for scheduled streams (2025-05-30T16:17:50.320Z to 2025-05-30T16:18:50.320Z)
2025-05-30T16:17:50.602Z [LOG] Checking for scheduled streams (2025-05-30T16:17:50.602Z to 2025-05-30T16:18:50.602Z)
2025-05-30T16:17:58.156Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:17:58.634Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:18:01.674Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:18:47.461Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:18:47.919Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:18:50.327Z [LOG] Checking for scheduled streams (2025-05-30T16:18:50.326Z to 2025-05-30T16:19:50.326Z)
2025-05-30T16:18:50.610Z [LOG] Checking for scheduled streams (2025-05-30T16:18:50.610Z to 2025-05-30T16:19:50.610Z)
2025-05-30T16:18:58.313Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:19:08.311Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:19:18.325Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:19:22.274Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:19:23.045Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:19:28.313Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:19:37.932Z [LOG] [Quota Check] User fb3211c5-297d-4098-a6a3-05b700c28635: 0/1 slots used
2025-05-30T16:19:50.335Z [LOG] Checking for scheduled streams (2025-05-30T16:19:50.334Z to 2025-05-30T16:20:50.334Z)
2025-05-30T16:19:50.620Z [LOG] Checking for scheduled streams (2025-05-30T16:19:50.620Z to 2025-05-30T16:20:50.620Z)
2025-05-30T16:20:17.150Z [LOG] User created successfully with ID: a864b58c-80d9-4af0-9e18-9b32f94e0599
2025-05-30T16:20:17.169Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:17.627Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:25.904Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:29.108Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:29.615Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:33.322Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:33.753Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:44.325Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:48.680Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:49.279Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:20:50.348Z [LOG] Checking for scheduled streams (2025-05-30T16:20:50.347Z to 2025-05-30T16:21:50.347Z)
2025-05-30T16:20:50.630Z [LOG] Checking for scheduled streams (2025-05-30T16:20:50.629Z to 2025-05-30T16:21:50.629Z)
2025-05-30T16:20:59.313Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:21:09.320Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:21:19.287Z [LOG] [Quota Check] User a864b58c-80d9-4af0-9e18-9b32f94e0599: 0/1 slots used
2025-05-30T16:21:50.322Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T16:21:50.325Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T16:21:50.329Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T16:21:50.332Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T16:21:50.336Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T16:21:50.339Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T16:21:50.342Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T16:21:50.343Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T16:21:50.355Z [LOG] Checking for scheduled streams (2025-05-30T16:21:50.354Z to 2025-05-30T16:22:50.354Z)
2025-05-30T16:21:50.634Z [LOG] Checking for scheduled streams (2025-05-30T16:21:50.633Z to 2025-05-30T16:22:50.633Z)
2025-05-30T16:22:09.213Z [LOG] User created successfully with ID: 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T16:22:09.229Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:22:09.866Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:22:13.230Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:22:16.041Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:22:16.605Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:22:27.322Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:22:33.787Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:22:37.308Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:22:47.311Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:22:50.356Z [LOG] Checking for scheduled streams (2025-05-30T16:22:50.355Z to 2025-05-30T16:23:50.355Z)
2025-05-30T16:22:50.642Z [LOG] Checking for scheduled streams (2025-05-30T16:22:50.642Z to 2025-05-30T16:23:50.642Z)
2025-05-30T16:22:57.313Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:23:07.314Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:23:17.317Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:23:50.360Z [LOG] Checking for scheduled streams (2025-05-30T16:23:50.359Z to 2025-05-30T16:24:50.359Z)
2025-05-30T16:23:50.652Z [LOG] Checking for scheduled streams (2025-05-30T16:23:50.650Z to 2025-05-30T16:24:50.650Z)
2025-05-30T16:24:18.633Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:24:50.370Z [LOG] Checking for scheduled streams (2025-05-30T16:24:50.369Z to 2025-05-30T16:25:50.369Z)
2025-05-30T16:24:50.659Z [LOG] Checking for scheduled streams (2025-05-30T16:24:50.658Z to 2025-05-30T16:25:50.658Z)
2025-05-30T16:25:18.359Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:25:50.373Z [LOG] Checking for scheduled streams (2025-05-30T16:25:50.372Z to 2025-05-30T16:26:50.372Z)
2025-05-30T16:25:50.664Z [LOG] Checking for scheduled streams (2025-05-30T16:25:50.664Z to 2025-05-30T16:26:50.664Z)
2025-05-30T16:26:18.325Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:26:50.327Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T16:26:50.331Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T16:26:50.336Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T16:26:50.338Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T16:26:50.343Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T16:26:50.347Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T16:26:50.351Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T16:26:50.353Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T16:26:50.414Z [LOG] Checking for scheduled streams (2025-05-30T16:26:50.411Z to 2025-05-30T16:27:50.411Z)
2025-05-30T16:26:50.671Z [LOG] Checking for scheduled streams (2025-05-30T16:26:50.671Z to 2025-05-30T16:27:50.671Z)
2025-05-30T16:27:17.023Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:27:17.618Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:27:18.327Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:27:21.071Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:27:21.668Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:27:27.560Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T16:27:32.323Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:33:18.982Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T16:33:20.246Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T16:33:20.249Z [LOG] Stream scheduler initialized
2025-05-30T16:33:20.251Z [LOG] Checking for scheduled streams (2025-05-30T16:33:20.251Z to 2025-05-30T16:34:20.251Z)
2025-05-30T16:33:20.276Z [LOG] Checking database schema...
2025-05-30T16:33:20.356Z [LOG] StreamFlow running at:
2025-05-30T16:33:20.357Z [LOG]   http://**************:7575
2025-05-30T16:33:20.358Z [LOG]   http://************:7575
2025-05-30T16:33:20.358Z [LOG]   http://*************:7575
2025-05-30T16:33:20.359Z [LOG]   http://***********:7575
2025-05-30T16:33:20.360Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T16:33:20.423Z [LOG] Scheduling termination for stream 0897b0b9-3de5-499a-814d-c370f9c6540d after 20.224566666666668 minutes
2025-05-30T16:33:20.424Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T16:33:20.424Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T16:33:20.425Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T16:33:20.461Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T16:33:20.469Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T16:33:20.477Z [LOG] Stream scheduler initialized
2025-05-30T16:33:20.478Z [LOG] Checking for scheduled streams (2025-05-30T16:33:20.478Z to 2025-05-30T16:34:20.478Z)
2025-05-30T16:33:20.480Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T16:33:20.488Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T16:33:20.493Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T16:33:20.494Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T16:33:20.502Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T16:33:20.505Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T16:33:20.510Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T16:33:20.511Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T16:33:21.390Z [LOG] ✅ Database migration completed
2025-05-30T16:33:21.391Z [LOG] Database migration completed successfully
2025-05-30T16:33:43.349Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:33:43.975Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:34:20.257Z [LOG] Checking for scheduled streams (2025-05-30T16:34:20.256Z to 2025-05-30T16:35:20.256Z)
2025-05-30T16:34:20.488Z [LOG] Checking for scheduled streams (2025-05-30T16:34:20.487Z to 2025-05-30T16:35:20.487Z)
2025-05-30T16:34:38.281Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:34:38.768Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:34:49.301Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:34:58.792Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:35:09.314Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:35:19.310Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:35:20.266Z [LOG] Checking for scheduled streams (2025-05-30T16:35:20.265Z to 2025-05-30T16:36:20.265Z)
2025-05-30T16:35:20.492Z [LOG] Checking for scheduled streams (2025-05-30T16:35:20.492Z to 2025-05-30T16:36:20.492Z)
2025-05-30T16:35:29.312Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:35:39.316Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:35:49.314Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:35:59.321Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:36:03.421Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748622963111-335000588.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748622963111-335000588.mp4',
  size: 42760993
}
2025-05-30T16:36:04.830Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T16:36:08.330Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:36:08.958Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:36:09.310Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:36:18.168Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'lv_0_20250519133637.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748622977338-628216997.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748622977338-628216997.mp4',
  size: 110732863
}
2025-05-30T16:36:18.287Z [LOG] [Upload] Video codec info: h264, Audio codec: aac
2025-05-30T16:36:20.279Z [LOG] Checking for scheduled streams (2025-05-30T16:36:20.279Z to 2025-05-30T16:37:20.279Z)
2025-05-30T16:36:20.506Z [LOG] Checking for scheduled streams (2025-05-30T16:36:20.505Z to 2025-05-30T16:37:20.505Z)
2025-05-30T16:36:21.840Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:36:22.353Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T16:36:47.793Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:36:48.798Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:36:59.315Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:37:09.306Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:37:19.318Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:37:20.290Z [LOG] Checking for scheduled streams (2025-05-30T16:37:20.289Z to 2025-05-30T16:38:20.289Z)
2025-05-30T16:37:20.509Z [LOG] Checking for scheduled streams (2025-05-30T16:37:20.509Z to 2025-05-30T16:38:20.509Z)
2025-05-30T16:37:29.329Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T16:37:39.318Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T17:13:07.567Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T17:13:08.355Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T17:13:08.357Z [LOG] Stream scheduler initialized
2025-05-30T17:13:08.358Z [LOG] Checking for scheduled streams (2025-05-30T17:13:08.358Z to 2025-05-30T17:14:08.358Z)
2025-05-30T17:13:08.373Z [LOG] Checking database schema...
2025-05-30T17:13:08.423Z [LOG] StreamFlow running at:
2025-05-30T17:13:08.424Z [LOG]   http://**************:7575
2025-05-30T17:13:08.425Z [LOG]   http://************:7575
2025-05-30T17:13:08.425Z [LOG]   http://*************:7575
2025-05-30T17:13:08.426Z [LOG]   http://***********:7575
2025-05-30T17:13:08.427Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T17:13:08.493Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T17:13:08.494Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T17:13:08.494Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T17:13:08.496Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T17:13:08.497Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T17:13:08.498Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T17:13:08.500Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T17:13:08.501Z [LOG] Stream scheduler initialized
2025-05-30T17:13:08.502Z [LOG] Checking for scheduled streams (2025-05-30T17:13:08.502Z to 2025-05-30T17:14:08.502Z)
2025-05-30T17:13:08.516Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T17:13:08.518Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T17:13:08.518Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T17:13:08.519Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T17:13:08.521Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T17:13:08.522Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T17:13:08.525Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T17:13:08.526Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T17:13:08.528Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T17:13:08.529Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T17:13:09.450Z [LOG] ✅ Database migration completed
2025-05-30T17:13:09.453Z [LOG] Database migration completed successfully
2025-05-30T17:13:26.642Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T17:13:27.132Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T17:13:37.308Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T17:13:42.214Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:13:42.760Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:13:46.809Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:13:47.307Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:13:47.361Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T17:13:57.322Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T17:13:57.525Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748625237173-362080218.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748625237173-362080218.mp4',
  size: 42760993
}
2025-05-30T17:13:57.677Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T17:14:01.191Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:01.686Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:05.186Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:05.662Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:07.312Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T17:14:08.363Z [LOG] Checking for scheduled streams (2025-05-30T17:14:08.362Z to 2025-05-30T17:15:08.362Z)
2025-05-30T17:14:08.366Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T17:14:08.367Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T17:14:08.511Z [LOG] Checking for scheduled streams (2025-05-30T17:14:08.510Z to 2025-05-30T17:15:08.510Z)
2025-05-30T17:14:08.513Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T17:14:08.520Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T17:14:16.318Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:17.309Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T17:14:19.856Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:20.337Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:25.637Z [ERROR] Error starting stream beef630f-5881-48eb-8a1e-4ccacbf38069: Error: Video record not found in database for video_id: 41a6c8f4-1c7f-4247-ade8-9621661572f8
    at buildFFmpegArgs (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:107:11)
    at async Object.startStream (C:\Users\<USER>\OriDrive\Desktop\streamflow\services\streamingService.js:266:24)
    at async C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:1777:22
2025-05-30T17:14:27.305Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T17:14:30.334Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:33.822Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:34.399Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:46.763Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'lv_0_20250519133637.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748625286065-102512310.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748625286065-102512310.mp4',
  size: 110732863
}
2025-05-30T17:14:46.865Z [LOG] [Upload] Video codec info: h264, Audio codec: aac
2025-05-30T17:14:50.667Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:14:51.158Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:15:01.157Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T17:15:08.371Z [LOG] Checking for scheduled streams (2025-05-30T17:15:08.371Z to 2025-05-30T17:16:08.371Z)
2025-05-30T17:15:08.382Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T17:15:08.383Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T17:15:08.519Z [LOG] Checking for scheduled streams (2025-05-30T17:15:08.519Z to 2025-05-30T17:16:08.519Z)
2025-05-30T17:15:08.521Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T17:15:08.522Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:10:34.676Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T22:10:51.837Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T22:10:51.839Z [LOG] Stream scheduler initialized
2025-05-30T22:10:51.840Z [LOG] Checking for scheduled streams (2025-05-30T22:10:51.840Z to 2025-05-30T22:11:51.840Z)
2025-05-30T22:10:51.854Z [LOG] Checking database schema...
2025-05-30T22:10:51.922Z [LOG] StreamFlow running at:
2025-05-30T22:10:51.923Z [LOG]   http://**************:7575
2025-05-30T22:10:51.924Z [LOG]   http://************:7575
2025-05-30T22:10:51.924Z [LOG]   http://*************:7575
2025-05-30T22:10:51.925Z [LOG]   http://172.19.96.1:7575
2025-05-30T22:10:51.926Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T22:10:52.018Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:10:52.019Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:10:52.020Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T22:10:52.021Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T22:10:52.022Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T22:10:52.069Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T22:10:52.074Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T22:10:52.079Z [LOG] Stream scheduler initialized
2025-05-30T22:10:52.081Z [LOG] Checking for scheduled streams (2025-05-30T22:10:52.081Z to 2025-05-30T22:11:52.081Z)
2025-05-30T22:10:52.084Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:10:52.087Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:10:52.087Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:10:52.089Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:10:52.093Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:10:52.095Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:10:52.102Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:10:52.104Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:10:52.109Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:10:52.111Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:10:53.031Z [LOG] ✅ Database migration completed
2025-05-30T22:10:53.032Z [LOG] Database migration completed successfully
2025-05-30T22:11:08.162Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:08.781Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:15.562Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:16.106Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:26.109Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:27.633Z [LOG] [StreamingService] Cleared failed status for stream f4f91b2e-1194-4c0d-bace-b187f1493521, was blacklisted: false
2025-05-30T22:11:29.545Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:30.021Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:38.646Z [LOG] [StreamingService] Cleared failed status for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, was blacklisted: false
2025-05-30T22:11:40.379Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:40.867Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:41.327Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:48.621Z [LOG] [StreamingService] Cleared failed status for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, was blacklisted: false
2025-05-30T22:11:50.418Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:51.123Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:51.846Z [LOG] Checking for scheduled streams (2025-05-30T22:11:51.846Z to 2025-05-30T22:12:51.846Z)
2025-05-30T22:11:51.851Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:11:51.852Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:11:52.087Z [LOG] Checking for scheduled streams (2025-05-30T22:11:52.086Z to 2025-05-30T22:12:52.086Z)
2025-05-30T22:11:52.091Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:11:52.093Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:11:56.613Z [LOG] [StreamingService] Cleared failed status for stream beef630f-5881-48eb-8a1e-4ccacbf38069, was blacklisted: false
2025-05-30T22:11:58.117Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:11:58.736Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:12:05.759Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:12:31.238Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:12:31.691Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:12:41.475Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:12:48.941Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:12:49.378Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:12:50.634Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:12:51.853Z [LOG] Checking for scheduled streams (2025-05-30T22:12:51.852Z to 2025-05-30T22:13:51.852Z)
2025-05-30T22:12:51.855Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:12:51.856Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:12:52.100Z [LOG] Checking for scheduled streams (2025-05-30T22:12:52.099Z to 2025-05-30T22:13:52.099Z)
2025-05-30T22:12:52.103Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:12:52.104Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:12:53.386Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:12:53.831Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:13:06.836Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748643186597-259147192.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748643186597-259147192.mp4',
  size: 42760993
}
2025-05-30T22:13:07.550Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T22:13:11.796Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:13:16.169Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:13:16.574Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:13:25.100Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'lv_0_20250519133637.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748643204448-190425266.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748643204448-190425266.mp4',
  size: 110732863
}
2025-05-30T22:13:25.238Z [LOG] [Upload] Video codec info: h264, Audio codec: aac
2025-05-30T22:13:28.552Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:13:31.675Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:13:32.076Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:13:35.683Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:13:51.859Z [LOG] Checking for scheduled streams (2025-05-30T22:13:51.859Z to 2025-05-30T22:14:51.859Z)
2025-05-30T22:13:51.862Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:13:51.863Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:13:52.110Z [LOG] Checking for scheduled streams (2025-05-30T22:13:52.110Z to 2025-05-30T22:14:52.110Z)
2025-05-30T22:13:52.113Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:13:52.114Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:14:51.871Z [LOG] Checking for scheduled streams (2025-05-30T22:14:51.870Z to 2025-05-30T22:15:51.870Z)
2025-05-30T22:14:51.874Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:14:51.875Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:14:52.120Z [LOG] Checking for scheduled streams (2025-05-30T22:14:52.119Z to 2025-05-30T22:15:52.119Z)
2025-05-30T22:14:52.124Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:14:52.125Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:14:57.760Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:14:58.219Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:15:03.348Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:15:03.775Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:15:13.779Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:15:23.781Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:15:33.781Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:15:43.774Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:15:51.846Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:15:51.848Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:15:51.852Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:15:51.853Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:15:51.855Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:15:51.857Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:15:51.859Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:15:51.861Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:15:51.883Z [LOG] Checking for scheduled streams (2025-05-30T22:15:51.883Z to 2025-05-30T22:16:51.883Z)
2025-05-30T22:15:51.885Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:15:51.886Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:15:52.131Z [LOG] Checking for scheduled streams (2025-05-30T22:15:52.130Z to 2025-05-30T22:16:52.130Z)
2025-05-30T22:15:52.133Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:15:52.134Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:15:53.789Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:16:03.784Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:16:13.775Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:16:23.773Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:16:33.788Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:16:43.788Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:16:51.893Z [LOG] Checking for scheduled streams (2025-05-30T22:16:51.893Z to 2025-05-30T22:17:51.893Z)
2025-05-30T22:16:51.897Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:16:51.897Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:16:52.144Z [LOG] Checking for scheduled streams (2025-05-30T22:16:52.144Z to 2025-05-30T22:17:52.144Z)
2025-05-30T22:16:52.147Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:16:52.148Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:16:53.778Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:17:03.774Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:17:13.772Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:17:23.784Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:17:33.784Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:17:43.782Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:17:51.896Z [LOG] Checking for scheduled streams (2025-05-30T22:17:51.896Z to 2025-05-30T22:18:51.896Z)
2025-05-30T22:17:51.899Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:17:51.899Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:17:52.156Z [LOG] Checking for scheduled streams (2025-05-30T22:17:52.155Z to 2025-05-30T22:18:52.155Z)
2025-05-30T22:17:52.158Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:17:52.159Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:17:53.790Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:18:03.773Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:18:13.783Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:18:23.778Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:18:33.778Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:18:43.772Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:18:49.288Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:18:50.036Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:18:51.901Z [LOG] Checking for scheduled streams (2025-05-30T22:18:51.901Z to 2025-05-30T22:19:51.901Z)
2025-05-30T22:18:51.904Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:18:51.905Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:18:52.162Z [LOG] Checking for scheduled streams (2025-05-30T22:18:52.161Z to 2025-05-30T22:19:52.161Z)
2025-05-30T22:18:52.167Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:18:52.168Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:19:00.008Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:10.007Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:20.006Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:20.929Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:25.072Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:25.498Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:29.029Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:34.894Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:35.370Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:37.431Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:40.426Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:40.931Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:41.347Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:50.149Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:50.695Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:51.910Z [LOG] Checking for scheduled streams (2025-05-30T22:19:51.910Z to 2025-05-30T22:20:51.910Z)
2025-05-30T22:19:51.913Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:19:51.914Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:19:52.163Z [LOG] Checking for scheduled streams (2025-05-30T22:19:52.162Z to 2025-05-30T22:20:52.162Z)
2025-05-30T22:19:52.166Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:19:52.167Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:19:52.644Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:19:53.055Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:20:03.064Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:20:13.062Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:20:24.101Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:20:24.542Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:20:26.343Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:20:28.701Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:20:29.106Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:20:30.664Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:20:51.855Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:20:51.857Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:20:51.859Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:20:51.860Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:20:51.863Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:20:51.864Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:20:51.867Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:20:51.868Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:20:51.916Z [LOG] Checking for scheduled streams (2025-05-30T22:20:51.915Z to 2025-05-30T22:21:51.915Z)
2025-05-30T22:20:51.918Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:20:51.919Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:20:52.164Z [LOG] Checking for scheduled streams (2025-05-30T22:20:52.164Z to 2025-05-30T22:21:52.164Z)
2025-05-30T22:20:52.166Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:20:52.167Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:21:51.928Z [LOG] Checking for scheduled streams (2025-05-30T22:21:51.928Z to 2025-05-30T22:22:51.928Z)
2025-05-30T22:21:51.931Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:21:51.932Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:21:52.164Z [LOG] Checking for scheduled streams (2025-05-30T22:21:52.164Z to 2025-05-30T22:22:52.164Z)
2025-05-30T22:21:52.167Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:21:52.168Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:22:51.941Z [LOG] Checking for scheduled streams (2025-05-30T22:22:51.940Z to 2025-05-30T22:23:51.940Z)
2025-05-30T22:22:51.943Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:22:51.944Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:22:52.177Z [LOG] Checking for scheduled streams (2025-05-30T22:22:52.177Z to 2025-05-30T22:23:52.177Z)
2025-05-30T22:22:52.179Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:22:52.180Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:23:26.385Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:23:40.544Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:23:51.944Z [LOG] Checking for scheduled streams (2025-05-30T22:23:51.944Z to 2025-05-30T22:24:51.944Z)
2025-05-30T22:23:51.946Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:23:51.947Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:23:52.190Z [LOG] Checking for scheduled streams (2025-05-30T22:23:52.190Z to 2025-05-30T22:24:52.190Z)
2025-05-30T22:23:52.192Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:23:52.193Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:24:51.947Z [LOG] Checking for scheduled streams (2025-05-30T22:24:51.946Z to 2025-05-30T22:25:51.946Z)
2025-05-30T22:24:51.949Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:24:51.950Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:24:52.198Z [LOG] Checking for scheduled streams (2025-05-30T22:24:52.198Z to 2025-05-30T22:25:52.198Z)
2025-05-30T22:24:52.201Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:24:52.202Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:25:51.866Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:25:51.868Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:25:51.869Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:25:51.871Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:25:51.872Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:25:51.873Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:25:51.876Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:25:51.877Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:25:51.949Z [LOG] Checking for scheduled streams (2025-05-30T22:25:51.948Z to 2025-05-30T22:26:51.948Z)
2025-05-30T22:25:51.952Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:25:51.953Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:25:52.204Z [LOG] Checking for scheduled streams (2025-05-30T22:25:52.204Z to 2025-05-30T22:26:52.204Z)
2025-05-30T22:25:52.207Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:25:52.208Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:26:51.955Z [LOG] Checking for scheduled streams (2025-05-30T22:26:51.955Z to 2025-05-30T22:27:51.955Z)
2025-05-30T22:26:51.960Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:26:51.961Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:26:52.219Z [LOG] Checking for scheduled streams (2025-05-30T22:26:52.218Z to 2025-05-30T22:27:52.218Z)
2025-05-30T22:26:52.222Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:26:52.223Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:27:51.962Z [LOG] Checking for scheduled streams (2025-05-30T22:27:51.961Z to 2025-05-30T22:28:51.961Z)
2025-05-30T22:27:51.965Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:27:51.966Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:27:52.230Z [LOG] Checking for scheduled streams (2025-05-30T22:27:52.229Z to 2025-05-30T22:28:52.229Z)
2025-05-30T22:27:52.233Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:27:52.234Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:28:51.973Z [LOG] Checking for scheduled streams (2025-05-30T22:28:51.972Z to 2025-05-30T22:29:51.972Z)
2025-05-30T22:28:51.975Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:28:51.976Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:28:52.241Z [LOG] Checking for scheduled streams (2025-05-30T22:28:52.240Z to 2025-05-30T22:29:52.240Z)
2025-05-30T22:28:52.243Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:28:52.244Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:29:34.704Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:29:36.936Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:29:42.224Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:29:42.656Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:29:43.500Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:29:46.627Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:29:51.987Z [LOG] Checking for scheduled streams (2025-05-30T22:29:51.986Z to 2025-05-30T22:30:51.986Z)
2025-05-30T22:29:51.992Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:29:51.993Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:29:52.254Z [LOG] Checking for scheduled streams (2025-05-30T22:29:52.254Z to 2025-05-30T22:30:52.254Z)
2025-05-30T22:29:52.256Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:29:52.257Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:30:12.972Z [LOG] Editing plan 9ee542b4-cb82-4433-bced-9e046fac0bbd - Checking name "Preview" - Found existing: 9ee542b4-cb82-4433-bced-9e046fac0bbd
2025-05-30T22:30:16.194Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:30:22.026Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:30:42.936Z [LOG] Editing plan 9ee542b4-cb82-4433-bced-9e046fac0bbd - Checking name "Preview" - Found existing: 9ee542b4-cb82-4433-bced-9e046fac0bbd
2025-05-30T22:30:49.371Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:30:51.019Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:30:51.866Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:30:51.869Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:30:51.871Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:30:51.872Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:30:51.876Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:30:51.877Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:30:51.881Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:30:51.883Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:30:51.998Z [LOG] Checking for scheduled streams (2025-05-30T22:30:51.997Z to 2025-05-30T22:31:51.997Z)
2025-05-30T22:30:52.000Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:30:52.001Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:30:52.268Z [LOG] Checking for scheduled streams (2025-05-30T22:30:52.267Z to 2025-05-30T22:31:52.267Z)
2025-05-30T22:30:52.271Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:30:52.272Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:31:04.533Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T22:31:14.521Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T22:31:14.523Z [LOG] Stream scheduler initialized
2025-05-30T22:31:14.525Z [LOG] Checking for scheduled streams (2025-05-30T22:31:14.524Z to 2025-05-30T22:32:14.524Z)
2025-05-30T22:31:14.540Z [LOG] Checking database schema...
2025-05-30T22:31:14.591Z [LOG] StreamFlow running at:
2025-05-30T22:31:14.592Z [LOG]   http://**************:7575
2025-05-30T22:31:14.593Z [LOG]   http://************:7575
2025-05-30T22:31:14.594Z [LOG]   http://*************:7575
2025-05-30T22:31:14.596Z [LOG]   http://172.19.96.1:7575
2025-05-30T22:31:14.597Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T22:31:14.663Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:31:14.664Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:31:14.665Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T22:31:14.666Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T22:31:14.667Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T22:31:14.670Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T22:31:14.672Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T22:31:14.673Z [LOG] Stream scheduler initialized
2025-05-30T22:31:14.674Z [LOG] Checking for scheduled streams (2025-05-30T22:31:14.674Z to 2025-05-30T22:32:14.674Z)
2025-05-30T22:31:14.676Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:31:14.677Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:31:14.679Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:31:14.680Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:31:14.682Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:31:14.683Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:31:14.686Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:31:14.687Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:31:14.688Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:31:14.690Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:31:15.672Z [LOG] ✅ Database migration completed
2025-05-30T22:31:15.673Z [LOG] Database migration completed successfully
2025-05-30T22:31:26.752Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:31:27.216Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:31:33.200Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:31:51.068Z [LOG] Editing plan 9ee542b4-cb82-4433-bced-9e046fac0bbd - Checking name "Preview" - Found existing: 9ee542b4-cb82-4433-bced-9e046fac0bbd
2025-05-30T22:31:57.548Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:32:00.157Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:32:14.528Z [LOG] Checking for scheduled streams (2025-05-30T22:32:14.527Z to 2025-05-30T22:33:14.527Z)
2025-05-30T22:32:14.530Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:32:14.532Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:32:14.688Z [LOG] Checking for scheduled streams (2025-05-30T22:32:14.687Z to 2025-05-30T22:33:14.687Z)
2025-05-30T22:32:14.691Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:32:14.692Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:32:14.818Z [LOG] Editing plan 9ee542b4-cb82-4433-bced-9e046fac0bbd - Checking name "Preview" - Found existing: 9ee542b4-cb82-4433-bced-9e046fac0bbd
2025-05-30T22:32:47.364Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:33:14.533Z [LOG] Checking for scheduled streams (2025-05-30T22:33:14.533Z to 2025-05-30T22:34:14.533Z)
2025-05-30T22:33:14.536Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:33:14.537Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:33:14.695Z [LOG] Checking for scheduled streams (2025-05-30T22:33:14.695Z to 2025-05-30T22:34:14.695Z)
2025-05-30T22:33:14.697Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:33:14.698Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:33:21.319Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:33:21.797Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:33:35.240Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748644415001-334067244.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748644415001-334067244.mp4',
  size: 42760993
}
2025-05-30T22:33:36.070Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T22:33:39.859Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:33:45.404Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:33:45.939Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:34:01.121Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748644440882-305241536.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748644440882-305241536.mp4',
  size: 42760993
}
2025-05-30T22:34:01.269Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T22:34:04.918Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:34:09.442Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:34:09.855Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:34:14.544Z [LOG] Checking for scheduled streams (2025-05-30T22:34:14.544Z to 2025-05-30T22:35:14.544Z)
2025-05-30T22:34:14.547Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:34:14.548Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:34:14.703Z [LOG] Checking for scheduled streams (2025-05-30T22:34:14.702Z to 2025-05-30T22:35:14.702Z)
2025-05-30T22:34:14.705Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:34:14.706Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:34:24.634Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:34:25.218Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:34:27.894Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:34:31.636Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:34:32.141Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:35:04.570Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:35:06.689Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:35:08.790Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:35:14.547Z [LOG] Checking for scheduled streams (2025-05-30T22:35:14.547Z to 2025-05-30T22:36:14.547Z)
2025-05-30T22:35:14.549Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:35:14.550Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:35:14.719Z [LOG] Checking for scheduled streams (2025-05-30T22:35:14.718Z to 2025-05-30T22:36:14.718Z)
2025-05-30T22:35:14.723Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:35:14.724Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:36:12.859Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T22:36:13.688Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T22:36:13.691Z [LOG] Stream scheduler initialized
2025-05-30T22:36:13.692Z [LOG] Checking for scheduled streams (2025-05-30T22:36:13.692Z to 2025-05-30T22:37:13.692Z)
2025-05-30T22:36:13.708Z [LOG] Checking database schema...
2025-05-30T22:36:13.774Z [LOG] StreamFlow running at:
2025-05-30T22:36:13.775Z [LOG]   http://**************:7575
2025-05-30T22:36:13.776Z [LOG]   http://************:7575
2025-05-30T22:36:13.777Z [LOG]   http://*************:7575
2025-05-30T22:36:13.780Z [LOG]   http://172.19.96.1:7575
2025-05-30T22:36:13.782Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T22:36:13.847Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T22:36:13.848Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T22:36:13.849Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T22:36:13.851Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:36:13.852Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:36:13.855Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T22:36:13.857Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T22:36:13.859Z [LOG] Stream scheduler initialized
2025-05-30T22:36:13.860Z [LOG] Checking for scheduled streams (2025-05-30T22:36:13.860Z to 2025-05-30T22:37:13.860Z)
2025-05-30T22:36:13.863Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:36:13.866Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:36:13.866Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:36:13.868Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:36:13.871Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:36:13.872Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:36:13.874Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:36:13.875Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:36:13.877Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:36:13.878Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:36:14.823Z [LOG] ✅ Database migration completed
2025-05-30T22:36:14.828Z [LOG] Database migration completed successfully
2025-05-30T22:36:19.790Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:20.299Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:23.103Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:26.217Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:26.667Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:27.276Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:32.062Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:32.481Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:39.922Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748644599675-137684755.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748644599675-137684755.mp4',
  size: 42760993
}
2025-05-30T22:36:40.056Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T22:36:46.265Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:46.686Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:36:48.515Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:13.704Z [LOG] Checking for scheduled streams (2025-05-30T22:37:13.703Z to 2025-05-30T22:38:13.703Z)
2025-05-30T22:37:13.706Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:37:13.707Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:37:13.871Z [LOG] Checking for scheduled streams (2025-05-30T22:37:13.871Z to 2025-05-30T22:38:13.871Z)
2025-05-30T22:37:13.874Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:37:13.875Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:37:13.944Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:14.413Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:16.658Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:22.304Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:25.240Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:25.641Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:37.561Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'lv_0_20250519133637.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748644656986-541577552.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748644656986-541577552.mp4',
  size: 110732863
}
2025-05-30T22:37:37.650Z [LOG] [Upload] Video codec info: h264, Audio codec: aac
2025-05-30T22:37:40.577Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:43.907Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:44.333Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:45.461Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:45.849Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:37:50.553Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:38:12.778Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:38:13.716Z [LOG] Checking for scheduled streams (2025-05-30T22:38:13.716Z to 2025-05-30T22:39:13.716Z)
2025-05-30T22:38:13.719Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:38:13.720Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:38:13.875Z [LOG] Checking for scheduled streams (2025-05-30T22:38:13.874Z to 2025-05-30T22:39:13.874Z)
2025-05-30T22:38:13.877Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:38:13.878Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:38:14.010Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:38:15.594Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:38:26.677Z [LOG] Editing plan 9ee542b4-cb82-4433-bced-9e046fac0bbd - Checking name "Preview" - Found existing: 9ee542b4-cb82-4433-bced-9e046fac0bbd
2025-05-30T22:38:32.837Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:39:06.103Z [LOG] Editing plan 9ee542b4-cb82-4433-bced-9e046fac0bbd - Checking name "Preview" - Found existing: 9ee542b4-cb82-4433-bced-9e046fac0bbd
2025-05-30T22:39:10.069Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:39:13.724Z [LOG] Checking for scheduled streams (2025-05-30T22:39:13.723Z to 2025-05-30T22:40:13.723Z)
2025-05-30T22:39:13.726Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:39:13.727Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:39:13.882Z [LOG] Checking for scheduled streams (2025-05-30T22:39:13.882Z to 2025-05-30T22:40:13.882Z)
2025-05-30T22:39:13.884Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:39:13.885Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:39:14.665Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:39:15.093Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:39:27.319Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748644767129-832319204.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748644767129-832319204.mp4',
  size: 42760993
}
2025-05-30T22:39:27.390Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T22:39:29.864Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:39:30.290Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:39:36.619Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:40:13.731Z [LOG] Checking for scheduled streams (2025-05-30T22:40:13.730Z to 2025-05-30T22:41:13.730Z)
2025-05-30T22:40:13.734Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:40:13.735Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:40:13.888Z [LOG] Checking for scheduled streams (2025-05-30T22:40:13.888Z to 2025-05-30T22:41:13.888Z)
2025-05-30T22:40:13.891Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:40:13.892Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:41:13.699Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:41:13.701Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:41:13.704Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:41:13.705Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:41:13.707Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:41:13.708Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:41:13.710Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:41:13.710Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:41:13.731Z [LOG] Checking for scheduled streams (2025-05-30T22:41:13.731Z to 2025-05-30T22:42:13.731Z)
2025-05-30T22:41:13.733Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:41:13.734Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:41:13.898Z [LOG] Checking for scheduled streams (2025-05-30T22:41:13.898Z to 2025-05-30T22:42:13.898Z)
2025-05-30T22:41:13.900Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:41:13.901Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:42:13.745Z [LOG] Checking for scheduled streams (2025-05-30T22:42:13.745Z to 2025-05-30T22:43:13.745Z)
2025-05-30T22:42:13.747Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:42:13.748Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:42:13.901Z [LOG] Checking for scheduled streams (2025-05-30T22:42:13.901Z to 2025-05-30T22:43:13.901Z)
2025-05-30T22:42:13.903Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:42:13.905Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:43:13.754Z [LOG] Checking for scheduled streams (2025-05-30T22:43:13.753Z to 2025-05-30T22:44:13.753Z)
2025-05-30T22:43:13.756Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:43:13.757Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:43:13.908Z [LOG] Checking for scheduled streams (2025-05-30T22:43:13.908Z to 2025-05-30T22:44:13.908Z)
2025-05-30T22:43:13.911Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:43:13.912Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:43:38.720Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T22:43:39.609Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T22:43:39.611Z [LOG] Stream scheduler initialized
2025-05-30T22:43:39.613Z [LOG] Checking for scheduled streams (2025-05-30T22:43:39.612Z to 2025-05-30T22:44:39.612Z)
2025-05-30T22:43:39.632Z [LOG] Checking database schema...
2025-05-30T22:43:39.696Z [LOG] StreamFlow running at:
2025-05-30T22:43:39.697Z [LOG]   http://**************:7575
2025-05-30T22:43:39.698Z [LOG]   http://************:7575
2025-05-30T22:43:39.699Z [LOG]   http://*************:7575
2025-05-30T22:43:39.700Z [LOG]   http://172.19.96.1:7575
2025-05-30T22:43:39.702Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T22:43:39.771Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:43:39.772Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:43:39.773Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T22:43:39.774Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T22:43:39.775Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T22:43:39.778Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T22:43:39.779Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T22:43:39.781Z [LOG] Stream scheduler initialized
2025-05-30T22:43:39.782Z [LOG] Checking for scheduled streams (2025-05-30T22:43:39.782Z to 2025-05-30T22:44:39.782Z)
2025-05-30T22:43:39.784Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:43:39.786Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:43:39.787Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:43:39.789Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:43:39.791Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:43:39.793Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:43:39.794Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:43:39.795Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:43:39.797Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:43:39.799Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:43:40.777Z [LOG] ✅ Database migration completed
2025-05-30T22:43:40.780Z [LOG] Database migration completed successfully
2025-05-30T22:43:50.344Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:43:50.798Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:43:55.138Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:44:04.159Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:44:04.717Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:44:07.091Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:44:20.537Z [LOG] Editing plan 9ee542b4-cb82-4433-bced-9e046fac0bbd - Checking name "Preview" - Found existing: 9ee542b4-cb82-4433-bced-9e046fac0bbd
2025-05-30T22:44:24.862Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:44:28.395Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:44:30.429Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:44:30.858Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:44:39.619Z [LOG] Checking for scheduled streams (2025-05-30T22:44:39.619Z to 2025-05-30T22:45:39.619Z)
2025-05-30T22:44:39.622Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:44:39.623Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:44:39.704Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:44:39.782Z [LOG] Checking for scheduled streams (2025-05-30T22:44:39.782Z to 2025-05-30T22:45:39.782Z)
2025-05-30T22:44:39.785Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:44:39.786Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:44:40.128Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:44:50.053Z [LOG] 🗑️ Deleting video: lv_0_20250519133637, Size: 0.103GB
2025-05-30T22:44:50.072Z [LOG] 📊 Updating storage: -0.103GB for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T22:44:50.077Z [LOG] ✅ Storage updated successfully
2025-05-30T22:44:53.291Z [LOG] 🗑️ Deleting video: lv_0_20250519133637, Size: 0.103GB
2025-05-30T22:44:53.314Z [LOG] 📊 Updating storage: -0.103GB for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T22:44:53.318Z [LOG] ✅ Storage updated successfully
2025-05-30T22:44:57.263Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:44:57.690Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:45:02.656Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:45:03.070Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:45:13.075Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:45:23.080Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:45:33.077Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:45:39.624Z [LOG] Checking for scheduled streams (2025-05-30T22:45:39.623Z to 2025-05-30T22:46:39.623Z)
2025-05-30T22:45:39.626Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:45:39.627Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:45:39.796Z [LOG] Checking for scheduled streams (2025-05-30T22:45:39.796Z to 2025-05-30T22:46:39.796Z)
2025-05-30T22:45:39.798Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:45:39.799Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:45:43.072Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:45:53.072Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:46:03.081Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:46:13.084Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:46:23.075Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:46:33.082Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:46:39.633Z [LOG] Checking for scheduled streams (2025-05-30T22:46:39.632Z to 2025-05-30T22:47:39.632Z)
2025-05-30T22:46:39.635Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:46:39.636Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:46:39.806Z [LOG] Checking for scheduled streams (2025-05-30T22:46:39.805Z to 2025-05-30T22:47:39.805Z)
2025-05-30T22:46:39.807Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:46:39.808Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:46:43.077Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:46:53.075Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:53:07.781Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T22:53:08.502Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T22:53:08.503Z [LOG] Stream scheduler initialized
2025-05-30T22:53:08.504Z [LOG] Checking for scheduled streams (2025-05-30T22:53:08.504Z to 2025-05-30T22:54:08.504Z)
2025-05-30T22:53:08.515Z [LOG] Checking database schema...
2025-05-30T22:53:08.565Z [LOG] StreamFlow running at:
2025-05-30T22:53:08.566Z [LOG]   http://**************:7575
2025-05-30T22:53:08.567Z [LOG]   http://************:7575
2025-05-30T22:53:08.568Z [LOG]   http://*************:7575
2025-05-30T22:53:08.569Z [LOG]   http://172.19.96.1:7575
2025-05-30T22:53:08.570Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T22:53:08.633Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:53:08.634Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:53:08.636Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T22:53:08.637Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T22:53:08.638Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T22:53:08.640Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T22:53:08.642Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T22:53:08.645Z [LOG] Stream scheduler initialized
2025-05-30T22:53:08.646Z [LOG] Checking for scheduled streams (2025-05-30T22:53:08.645Z to 2025-05-30T22:54:08.645Z)
2025-05-30T22:53:08.648Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T22:53:08.651Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:53:08.652Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:53:08.654Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T22:53:08.656Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T22:53:08.658Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T22:53:08.660Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T22:53:08.661Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T22:53:08.663Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T22:53:08.664Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T22:53:09.594Z [LOG] ✅ Database migration completed
2025-05-30T22:53:09.598Z [LOG] Database migration completed successfully
2025-05-30T22:53:15.821Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:53:16.281Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:53:20.956Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:53:21.395Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:53:25.691Z [LOG] 🗑️ Deleting video: RUID76e0ccfcbf844c19a40cf21a5e9a9d32, Size: 0.040GB
2025-05-30T22:53:25.705Z [LOG] 📊 Updating storage: -0.040GB for user 28fd3c08-af4b-4fe8-9b36-12e0089bd409
2025-05-30T22:53:25.710Z [LOG] ✅ Storage updated successfully
2025-05-30T22:53:29.421Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:53:29.904Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:53:34.985Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:53:39.619Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:53:51.744Z [LOG] Editing plan 9ee542b4-cb82-4433-bced-9e046fac0bbd - Checking name "Preview" - Found existing: 9ee542b4-cb82-4433-bced-9e046fac0bbd
2025-05-30T22:53:55.422Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:54:00.380Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:54:00.827Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:54:06.105Z [LOG] 🗑️ Deleting video: RUID76e0ccfcbf844c19a40cf21a5e9a9d32, Size: 0.040GB
2025-05-30T22:54:06.116Z [LOG] 📊 Updating storage: -0.040GB for user 28fd3c08-af4b-4fe8-9b36-12e0089bd409
2025-05-30T22:54:06.122Z [LOG] ✅ Storage updated successfully
2025-05-30T22:54:08.515Z [LOG] Checking for scheduled streams (2025-05-30T22:54:08.515Z to 2025-05-30T22:55:08.515Z)
2025-05-30T22:54:08.517Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:54:08.518Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:54:08.590Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:54:08.654Z [LOG] Checking for scheduled streams (2025-05-30T22:54:08.654Z to 2025-05-30T22:55:08.654Z)
2025-05-30T22:54:08.657Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:54:08.659Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:54:09.091Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:54:11.902Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:54:15.796Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:54:18.978Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:54:19.398Z [LOG] [Quota Check] User 28fd3c08-af4b-4fe8-9b36-12e0089bd409: 3/3 slots used
2025-05-30T22:54:29.525Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:54:29.957Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:54:35.346Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:54:49.116Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'lv_0_20250519133637.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748645688473-672797557.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748645688473-672797557.mp4',
  size: 110732863
}
2025-05-30T22:54:49.280Z [LOG] [Upload] Video codec info: h264, Audio codec: aac
2025-05-30T22:54:52.906Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:54:56.527Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:54:57.003Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:54:59.115Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:55:02.219Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:55:02.641Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:55:04.295Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:55:04.693Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:55:08.517Z [LOG] Checking for scheduled streams (2025-05-30T22:55:08.517Z to 2025-05-30T22:56:08.517Z)
2025-05-30T22:55:08.521Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:55:08.521Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:55:08.659Z [LOG] Checking for scheduled streams (2025-05-30T22:55:08.659Z to 2025-05-30T22:56:08.659Z)
2025-05-30T22:55:08.661Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:55:08.662Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:55:28.276Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'lv_0_20250519133637.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748645727644-482346462.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748645727644-482346462.mp4',
  size: 110732863
}
2025-05-30T22:55:28.371Z [LOG] [Upload] Video codec info: h264, Audio codec: aac
2025-05-30T22:55:31.357Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:55:31.811Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:55:34.569Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:55:38.890Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T22:56:08.524Z [LOG] Checking for scheduled streams (2025-05-30T22:56:08.523Z to 2025-05-30T22:57:08.523Z)
2025-05-30T22:56:08.526Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:56:08.527Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:56:08.660Z [LOG] Checking for scheduled streams (2025-05-30T22:56:08.660Z to 2025-05-30T22:57:08.660Z)
2025-05-30T22:56:08.663Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:56:08.664Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:57:08.530Z [LOG] Checking for scheduled streams (2025-05-30T22:57:08.529Z to 2025-05-30T22:58:08.529Z)
2025-05-30T22:57:08.532Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:57:08.533Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T22:57:08.666Z [LOG] Checking for scheduled streams (2025-05-30T22:57:08.666Z to 2025-05-30T22:58:08.666Z)
2025-05-30T22:57:08.669Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T22:57:08.670Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:01:38.359Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T23:01:39.098Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T23:01:39.099Z [LOG] Stream scheduler initialized
2025-05-30T23:01:39.100Z [LOG] Checking for scheduled streams (2025-05-30T23:01:39.100Z to 2025-05-30T23:02:39.100Z)
2025-05-30T23:01:39.115Z [LOG] Checking database schema...
2025-05-30T23:01:39.164Z [LOG] StreamFlow running at:
2025-05-30T23:01:39.165Z [LOG]   http://**************:7575
2025-05-30T23:01:39.166Z [LOG]   http://************:7575
2025-05-30T23:01:39.166Z [LOG]   http://*************:7575
2025-05-30T23:01:39.167Z [LOG]   http://172.19.96.1:7575
2025-05-30T23:01:39.167Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T23:01:39.225Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T23:01:39.226Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T23:01:39.227Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T23:01:39.229Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:01:39.230Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:01:39.231Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T23:01:39.233Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T23:01:39.234Z [LOG] Stream scheduler initialized
2025-05-30T23:01:39.235Z [LOG] Checking for scheduled streams (2025-05-30T23:01:39.234Z to 2025-05-30T23:02:39.234Z)
2025-05-30T23:01:39.236Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T23:01:39.237Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:01:39.238Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:01:39.239Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T23:01:39.241Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T23:01:39.241Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T23:01:39.244Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T23:01:39.245Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T23:01:39.246Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T23:01:39.247Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T23:01:40.235Z [LOG] ✅ Database migration completed
2025-05-30T23:01:40.236Z [LOG] Database migration completed successfully
2025-05-30T23:01:52.443Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:01:52.921Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:01:57.040Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:01:59.813Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:02:10.764Z [LOG] 🗑️ Deleting video: lv_0_20250519133637, Size: 0.103GB
2025-05-30T23:02:10.784Z [LOG] 📊 Updating storage: -0.103GB for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:02:10.786Z [WARN] ⚠️ Storage would go negative for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0GB + -0.10312801506370306GB = -0.10312801506370306GB. Setting to 0GB instead.
2025-05-30T23:02:10.792Z [LOG] ✅ Storage updated successfully
2025-05-30T23:02:18.168Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:02:18.623Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:02:21.812Z [LOG] 🗑️ Deleting video: lv_0_20250519133637, Size: 0.103GB
2025-05-30T23:02:21.832Z [LOG] 📊 Updating storage: -0.103GB for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:02:21.833Z [WARN] ⚠️ Storage would go negative for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0GB + -0.10312801506370306GB = -0.10312801506370306GB. Setting to 0GB instead.
2025-05-30T23:02:21.837Z [LOG] ✅ Storage updated successfully
2025-05-30T23:02:24.682Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:02:25.109Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:02:31.403Z [LOG] 🔍 Storage quota check for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:02:31.405Z [LOG] 🔍 Upload size: no file = 0.000GB
2025-05-30T23:02:31.408Z [LOG] 🔍 Storage check result: current=0GB, max=0.146484375GB, would exceed=false
2025-05-30T23:02:31.409Z [LOG] 🔍 Stored uploadSizeGB: 0GB for later storage update
2025-05-30T23:02:31.675Z [LOG] 🔍 Storage update middleware called for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:02:31.676Z [LOG] 🔍 Upload size: 0GB, File: 42760993 bytes
2025-05-30T23:02:31.678Z [LOG] ⚠️ Storage update skipped - uploadSizeGB: 0, userId: 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:02:31.681Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748646151417-783219317.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748646151417-783219317.mp4',
  size: 42760993
}
2025-05-30T23:02:31.814Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T23:02:35.136Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:02:35.574Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:02:39.104Z [LOG] Checking for scheduled streams (2025-05-30T23:02:39.103Z to 2025-05-30T23:03:39.103Z)
2025-05-30T23:02:39.108Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:02:39.109Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:02:39.239Z [LOG] Checking for scheduled streams (2025-05-30T23:02:39.238Z to 2025-05-30T23:03:39.238Z)
2025-05-30T23:02:39.241Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:02:39.242Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:02:40.949Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:02:55.127Z [LOG] 🔍 Storage quota check for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:02:55.128Z [LOG] 🔍 Upload size: no file = 0.000GB
2025-05-30T23:02:55.130Z [LOG] 🔍 Storage check result: current=0GB, max=0.146484375GB, would exceed=false
2025-05-30T23:02:55.131Z [LOG] 🔍 Stored uploadSizeGB: 0GB for later storage update
2025-05-30T23:02:55.405Z [LOG] 🔍 Storage update middleware called for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:02:55.406Z [LOG] 🔍 Upload size: 0GB, File: 42760993 bytes
2025-05-30T23:02:55.407Z [LOG] ⚠️ Storage update skipped - uploadSizeGB: 0, userId: 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:02:55.410Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748646175133-18615565.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748646175133-18615565.mp4',
  size: 42760993
}
2025-05-30T23:02:55.481Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T23:03:00.847Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:01.336Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:04.832Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:07.251Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:11.523Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:11.943Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:28.145Z [LOG] 🔍 Storage quota check for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:03:28.146Z [LOG] 🔍 Upload size: no file = 0.000GB
2025-05-30T23:03:28.148Z [LOG] 🔍 Storage check result: current=0GB, max=0.146484375GB, would exceed=false
2025-05-30T23:03:28.150Z [LOG] 🔍 Stored uploadSizeGB: 0GB for later storage update
2025-05-30T23:03:28.807Z [LOG] 🔍 Storage update middleware called for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:03:28.808Z [LOG] 🔍 Upload size: 0GB, File: 110732863 bytes
2025-05-30T23:03:28.808Z [LOG] ⚠️ Storage update skipped - uploadSizeGB: 0, userId: 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:03:28.811Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'lv_0_20250519133637.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748646208153-323445399.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748646208153-323445399.mp4',
  size: 110732863
}
2025-05-30T23:03:28.896Z [LOG] [Upload] Video codec info: h264, Audio codec: aac
2025-05-30T23:03:31.646Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:32.076Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:35.904Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:37.217Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:37.651Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:39.108Z [LOG] Checking for scheduled streams (2025-05-30T23:03:39.107Z to 2025-05-30T23:04:39.107Z)
2025-05-30T23:03:39.111Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:03:39.112Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:03:39.249Z [LOG] Checking for scheduled streams (2025-05-30T23:03:39.248Z to 2025-05-30T23:04:39.248Z)
2025-05-30T23:03:39.251Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:03:39.252Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:03:47.656Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:03:57.664Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:04:07.664Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:04:17.668Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:04:27.678Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:04:59.216Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T23:04:59.986Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T23:04:59.989Z [LOG] Stream scheduler initialized
2025-05-30T23:04:59.990Z [LOG] Checking for scheduled streams (2025-05-30T23:04:59.990Z to 2025-05-30T23:05:59.990Z)
2025-05-30T23:05:00.005Z [LOG] Checking database schema...
2025-05-30T23:05:00.061Z [LOG] StreamFlow running at:
2025-05-30T23:05:00.062Z [LOG]   http://**************:7575
2025-05-30T23:05:00.063Z [LOG]   http://************:7575
2025-05-30T23:05:00.064Z [LOG]   http://*************:7575
2025-05-30T23:05:00.065Z [LOG]   http://172.19.96.1:7575
2025-05-30T23:05:00.066Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T23:05:00.130Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:05:00.131Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:05:00.135Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T23:05:00.136Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T23:05:00.137Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T23:05:00.141Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T23:05:00.143Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T23:05:00.145Z [LOG] Stream scheduler initialized
2025-05-30T23:05:00.146Z [LOG] Checking for scheduled streams (2025-05-30T23:05:00.146Z to 2025-05-30T23:06:00.146Z)
2025-05-30T23:05:00.148Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T23:05:00.149Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:05:00.150Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:05:00.151Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T23:05:00.153Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T23:05:00.154Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T23:05:00.156Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T23:05:00.158Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T23:05:00.159Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T23:05:00.160Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T23:05:01.083Z [LOG] ✅ Database migration completed
2025-05-30T23:05:01.085Z [LOG] Database migration completed successfully
2025-05-30T23:05:14.998Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:05:15.525Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:05:21.347Z [LOG] 🗑️ Deleting video: lv_0_20250519133637, Size: 0.103GB
2025-05-30T23:05:21.379Z [LOG] 📊 Updating storage: -0.103GB for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:05:21.382Z [WARN] ⚠️ Storage would go negative for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0GB + -0.10312801506370306GB = -0.10312801506370306GB. Setting to 0GB instead.
2025-05-30T23:05:21.390Z [LOG] ✅ Storage updated successfully
2025-05-30T23:05:24.462Z [LOG] 🗑️ Deleting video: RUID76e0ccfcbf844c19a40cf21a5e9a9d32, Size: 0.040GB
2025-05-30T23:05:24.489Z [LOG] 📊 Updating storage: -0.040GB for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:05:24.493Z [WARN] ⚠️ Storage would go negative for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0GB + -0.039824278093874454GB = -0.039824278093874454GB. Setting to 0GB instead.
2025-05-30T23:05:24.504Z [LOG] ✅ Storage updated successfully
2025-05-30T23:05:27.518Z [LOG] 🗑️ Deleting video: RUID76e0ccfcbf844c19a40cf21a5e9a9d32, Size: 0.040GB
2025-05-30T23:05:27.545Z [LOG] 📊 Updating storage: -0.040GB for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:05:27.548Z [WARN] ⚠️ Storage would go negative for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0GB + -0.039824278093874454GB = -0.039824278093874454GB. Setting to 0GB instead.
2025-05-30T23:05:27.556Z [LOG] ✅ Storage updated successfully
2025-05-30T23:05:35.531Z [LOG] 🔍 Storage quota check for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:05:35.536Z [LOG] 🔍 Upload size: 42760993 bytes = 0.040GB
2025-05-30T23:05:35.542Z [LOG] 🔍 Storage check result: current=0GB, max=0.146484375GB, would exceed=false
2025-05-30T23:05:35.544Z [LOG] 🔍 Stored uploadSizeGB: 0.039824278093874454GB for later storage update
2025-05-30T23:05:35.546Z [LOG] 🔍 Storage update middleware called for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:05:35.547Z [LOG] 🔍 Upload size: 0.039824278093874454GB, File: 42760993 bytes
2025-05-30T23:05:35.549Z [LOG] 📊 Updating storage: +0.040GB for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:05:35.579Z [LOG] ✅ Storage updated successfully
2025-05-30T23:05:35.583Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'RUID76e0ccfcbf844c19a40cf21a5e9a9d32.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748646335023-595828757.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748646335023-595828757.mp4',
  size: 42760993
}
2025-05-30T23:05:35.749Z [LOG] [Upload] Video codec info: hevc, Audio codec: aac
2025-05-30T23:05:38.798Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:05:39.289Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:05:40.746Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:05:51.436Z [LOG] 🔍 Storage quota check for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:05:51.437Z [LOG] 🔍 Upload size: 110732863 bytes = 0.103GB
2025-05-30T23:05:51.449Z [LOG] 🔍 Storage check result: current=0.039824278093874454GB, max=0.146484375GB, would exceed=false
2025-05-30T23:05:51.451Z [LOG] 🔍 Stored uploadSizeGB: 0.10312801506370306GB for later storage update
2025-05-30T23:05:51.452Z [LOG] 🔍 Storage update middleware called for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:05:51.453Z [LOG] 🔍 Upload size: 0.10312801506370306GB, File: 110732863 bytes
2025-05-30T23:05:51.455Z [LOG] 📊 Updating storage: +0.103GB for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:05:51.463Z [LOG] ✅ Storage updated successfully
2025-05-30T23:05:51.468Z [LOG] Upload request received: {
  fieldname: 'video',
  originalname: 'lv_0_20250519133637.mp4',
  encoding: '7bit',
  mimetype: 'video/mp4',
  destination: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos',
  filename: 'video-1748646350736-889593732.mp4',
  path: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\video-1748646350736-889593732.mp4',
  size: 110732863
}
2025-05-30T23:05:51.595Z [LOG] [Upload] Video codec info: h264, Audio codec: aac
2025-05-30T23:05:56.021Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:05:59.993Z [LOG] Checking for scheduled streams (2025-05-30T23:05:59.993Z to 2025-05-30T23:06:59.993Z)
2025-05-30T23:05:59.997Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:05:59.999Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:06:00.149Z [LOG] Checking for scheduled streams (2025-05-30T23:06:00.148Z to 2025-05-30T23:07:00.148Z)
2025-05-30T23:06:00.151Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:06:00.152Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:06:05.707Z [LOG] 🔍 Storage quota check for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:06:05.708Z [LOG] 🔍 Upload size: 42760993 bytes = 0.040GB
2025-05-30T23:06:05.711Z [LOG] 🔍 Storage check result: current=0.14295229315757751GB, max=0.146484375GB, would exceed=true
2025-05-30T23:06:19.371Z [LOG] 🔍 Storage quota check for user 8667e873-0470-4e45-b2f5-0fafcf27e1fb
2025-05-30T23:06:19.372Z [LOG] 🔍 Upload size: 110732863 bytes = 0.103GB
2025-05-30T23:06:19.375Z [LOG] 🔍 Storage check result: current=0.14295229315757751GB, max=0.146484375GB, would exceed=true
2025-05-30T23:07:00.003Z [LOG] Checking for scheduled streams (2025-05-30T23:07:00.002Z to 2025-05-30T23:08:00.002Z)
2025-05-30T23:07:00.012Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:07:00.014Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:07:00.160Z [LOG] Checking for scheduled streams (2025-05-30T23:07:00.159Z to 2025-05-30T23:08:00.159Z)
2025-05-30T23:07:00.163Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:07:00.164Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:08:00.018Z [LOG] Checking for scheduled streams (2025-05-30T23:08:00.017Z to 2025-05-30T23:09:00.017Z)
2025-05-30T23:08:00.023Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:08:00.025Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:08:00.163Z [LOG] Checking for scheduled streams (2025-05-30T23:08:00.163Z to 2025-05-30T23:09:00.163Z)
2025-05-30T23:08:00.165Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:08:00.166Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:09:03.350Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T23:09:04.087Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T23:09:04.089Z [LOG] Stream scheduler initialized
2025-05-30T23:09:04.090Z [LOG] Checking for scheduled streams (2025-05-30T23:09:04.090Z to 2025-05-30T23:10:04.090Z)
2025-05-30T23:09:04.103Z [LOG] Checking database schema...
2025-05-30T23:09:04.153Z [LOG] StreamFlow running at:
2025-05-30T23:09:04.154Z [LOG]   http://**************:7575
2025-05-30T23:09:04.155Z [LOG]   http://************:7575
2025-05-30T23:09:04.156Z [LOG]   http://*************:7575
2025-05-30T23:09:04.157Z [LOG]   http://172.19.96.1:7575
2025-05-30T23:09:04.158Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T23:09:04.229Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:09:04.230Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:09:04.233Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T23:09:04.234Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T23:09:04.235Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T23:09:04.238Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T23:09:04.241Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T23:09:04.245Z [LOG] Stream scheduler initialized
2025-05-30T23:09:04.246Z [LOG] Checking for scheduled streams (2025-05-30T23:09:04.245Z to 2025-05-30T23:10:04.245Z)
2025-05-30T23:09:04.249Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T23:09:04.251Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:09:04.252Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:09:04.254Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T23:09:04.257Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T23:09:04.258Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T23:09:04.261Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T23:09:04.262Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T23:09:04.265Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T23:09:04.266Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T23:09:05.172Z [LOG] ✅ Database migration completed
2025-05-30T23:09:05.174Z [LOG] Database migration completed successfully
2025-05-30T23:09:10.302Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:09:10.747Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:10:04.098Z [LOG] Checking for scheduled streams (2025-05-30T23:10:04.097Z to 2025-05-30T23:11:04.097Z)
2025-05-30T23:10:04.101Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:10:04.102Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:10:04.254Z [LOG] Checking for scheduled streams (2025-05-30T23:10:04.253Z to 2025-05-30T23:11:04.253Z)
2025-05-30T23:10:04.256Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:10:04.256Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:11:04.099Z [LOG] Checking for scheduled streams (2025-05-30T23:11:04.098Z to 2025-05-30T23:12:04.098Z)
2025-05-30T23:11:04.102Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:11:04.103Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:11:04.268Z [LOG] Checking for scheduled streams (2025-05-30T23:11:04.268Z to 2025-05-30T23:12:04.268Z)
2025-05-30T23:11:04.270Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:11:04.271Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:12:04.109Z [LOG] Checking for scheduled streams (2025-05-30T23:12:04.109Z to 2025-05-30T23:13:04.109Z)
2025-05-30T23:12:04.112Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:12:04.113Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:12:04.269Z [LOG] Checking for scheduled streams (2025-05-30T23:12:04.269Z to 2025-05-30T23:13:04.269Z)
2025-05-30T23:12:04.271Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:12:04.272Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:13:04.118Z [LOG] Checking for scheduled streams (2025-05-30T23:13:04.117Z to 2025-05-30T23:14:04.117Z)
2025-05-30T23:13:04.122Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:13:04.124Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:13:04.284Z [LOG] Checking for scheduled streams (2025-05-30T23:13:04.283Z to 2025-05-30T23:14:04.283Z)
2025-05-30T23:13:04.287Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:13:04.288Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:15:10.114Z [LOG] Logger initialized. Output will be written to console and logs/app.log
2025-05-30T23:15:10.982Z [LOG] Using bundled FFmpeg at: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffmpeg-installer\win32-x64\ffmpeg.exe
2025-05-30T23:15:10.984Z [LOG] Stream scheduler initialized
2025-05-30T23:15:10.985Z [LOG] Checking for scheduled streams (2025-05-30T23:15:10.985Z to 2025-05-30T23:16:10.985Z)
2025-05-30T23:15:10.998Z [LOG] Checking database schema...
2025-05-30T23:15:11.055Z [LOG] StreamFlow running at:
2025-05-30T23:15:11.056Z [LOG]   http://**************:7575
2025-05-30T23:15:11.057Z [LOG]   http://************:7575
2025-05-30T23:15:11.058Z [LOG]   http://*************:7575
2025-05-30T23:15:11.059Z [LOG]   http://172.19.96.1:7575
2025-05-30T23:15:11.060Z [LOG] [StreamingService] Cleaning up orphaned streams...
2025-05-30T23:15:11.119Z [LOG] [StreamingService] Found 3 streams marked as 'live' in database
2025-05-30T23:15:11.120Z [LOG] [StreamingService] Marking all as offline since server restarted (streams cannot survive restart)
2025-05-30T23:15:11.121Z [LOG] [StreamingService] Marking stream f4f91b2e-1194-4c0d-bace-b187f1493521 as offline (server restart cleanup)
2025-05-30T23:15:11.123Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:15:11.124Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:15:11.126Z [LOG] [StreamingService] Marking stream 0897b0b9-3de5-499a-814d-c370f9c6540d as offline (server restart cleanup)
2025-05-30T23:15:11.128Z [LOG] [StreamingService] Marking stream beef630f-5881-48eb-8a1e-4ccacbf38069 as offline (server restart cleanup)
2025-05-30T23:15:11.129Z [LOG] Stream scheduler initialized
2025-05-30T23:15:11.130Z [LOG] Checking for scheduled streams (2025-05-30T23:15:11.130Z to 2025-05-30T23:16:11.130Z)
2025-05-30T23:15:11.132Z [LOG] [StreamingService] Syncing stream statuses...
2025-05-30T23:15:11.133Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:15:11.134Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:15:11.135Z [LOG] [StreamingService] Found inconsistent stream f4f91b2e-1194-4c0d-bace-b187f1493521: marked as 'live' in DB but not active in memory
2025-05-30T23:15:11.137Z [LOG] [StreamingService] Updated stream f4f91b2e-1194-4c0d-bace-b187f1493521 status to 'offline'
2025-05-30T23:15:11.138Z [LOG] [StreamingService] Found inconsistent stream 0897b0b9-3de5-499a-814d-c370f9c6540d: marked as 'live' in DB but not active in memory
2025-05-30T23:15:11.140Z [LOG] [StreamingService] Updated stream 0897b0b9-3de5-499a-814d-c370f9c6540d status to 'offline'
2025-05-30T23:15:11.141Z [LOG] [StreamingService] Found inconsistent stream beef630f-5881-48eb-8a1e-4ccacbf38069: marked as 'live' in DB but not active in memory
2025-05-30T23:15:11.142Z [LOG] [StreamingService] Updated stream beef630f-5881-48eb-8a1e-4ccacbf38069 status to 'offline'
2025-05-30T23:15:11.143Z [LOG] [StreamingService] Stream status sync completed. Active streams: 0
2025-05-30T23:15:12.074Z [LOG] ✅ Database migration completed
2025-05-30T23:15:12.076Z [LOG] Database migration completed successfully
2025-05-30T23:15:17.274Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:15:17.972Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:15:22.277Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:15:24.964Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:15:25.468Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:15:30.783Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:15:37.800Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T23:15:39.456Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T23:15:39.957Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T23:15:43.599Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T23:15:44.043Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T23:15:45.091Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/1 slots used
2025-05-30T23:15:47.594Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:15:49.050Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:15:49.510Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:16:07.398Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:16:07.870Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:16:09.855Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:16:10.988Z [LOG] Checking for scheduled streams (2025-05-30T23:16:10.987Z to 2025-05-30T23:17:10.987Z)
2025-05-30T23:16:10.995Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:16:10.998Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:16:11.145Z [LOG] Checking for scheduled streams (2025-05-30T23:16:11.144Z to 2025-05-30T23:17:11.144Z)
2025-05-30T23:16:11.152Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:16:11.155Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:16:43.788Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:16:44.383Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:16:54.385Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:17:04.409Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:17:11.001Z [LOG] Checking for scheduled streams (2025-05-30T23:17:11.000Z to 2025-05-30T23:18:11.000Z)
2025-05-30T23:17:11.004Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:17:11.006Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:17:11.155Z [LOG] Checking for scheduled streams (2025-05-30T23:17:11.154Z to 2025-05-30T23:18:11.154Z)
2025-05-30T23:17:11.157Z [LOG] Stream 0897b0b9-3de5-499a-814d-c370f9c6540d exceeded duration, stopping now
2025-05-30T23:17:11.158Z [LOG] [StreamingService] Stop request for stream 0897b0b9-3de5-499a-814d-c370f9c6540d, isActive: false
2025-05-30T23:17:14.402Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
2025-05-30T23:17:24.377Z [LOG] [Quota Check] User 8667e873-0470-4e45-b2f5-0fafcf27e1fb: 0/0 slots used
